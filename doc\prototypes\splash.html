<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 启动页面</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5A4FCF',
                        'secondary': '#74B9FF',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E17055',
                        'light': '#DDD6FE',
                        'surface': 'rgba(255, 255, 255, 0.1)',
                        'glass': 'rgba(255, 255, 255, 0.15)',
                    }
                }
            }
        }
    </script>
    <style>
        .splash-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 50%, #74B9FF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 
                0 25px 50px rgba(108, 92, 231, 0.4),
                0 10px 25px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .logo-animation {
            animation: logoFloat 3s ease-in-out infinite;
        }
        
        .loading-animation {
            animation: spin 1.5s linear infinite;
        }
        
        .text-glow {
            text-shadow: 
                0 0 20px rgba(255, 255, 255, 0.5),
                0 0 40px rgba(255, 255, 255, 0.3),
                0 0 60px rgba(255, 255, 255, 0.1);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        @keyframes logoFloat {
            0%, 100% { 
                transform: translateY(0px) rotate(0deg) scale(1); 
                filter: drop-shadow(0 10px 20px rgba(108, 92, 231, 0.3));
            }
            33% { 
                transform: translateY(-15px) rotate(2deg) scale(1.05); 
                filter: drop-shadow(0 20px 40px rgba(108, 92, 231, 0.4));
            }
            66% { 
                transform: translateY(-8px) rotate(-1deg) scale(1.02); 
                filter: drop-shadow(0 15px 30px rgba(108, 92, 231, 0.35));
            }
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(40px);
            opacity: 0.6;
            animation: float 8s ease-in-out infinite;
        }
        
        .orb-1 {
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(253, 121, 168, 0.8) 0%, transparent 70%);
            top: -150px;
            left: -100px;
            animation-delay: 0s;
        }
        
        .orb-2 {
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(116, 185, 255, 0.6) 0%, transparent 70%);
            bottom: -100px;
            right: -50px;
            animation-delay: 2s;
        }
        
        .orb-3 {
            width: 150px;
            height: 150px;
            background: radial-gradient(circle, rgba(253, 203, 110, 0.5) 0%, transparent 70%);
            top: 200px;
            right: -75px;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) translateX(0px) rotate(0deg); }
            25% { transform: translateY(-20px) translateX(10px) rotate(90deg); }
            50% { transform: translateY(0px) translateX(-15px) rotate(180deg); }
            75% { transform: translateY(15px) translateX(5px) rotate(270deg); }
        }
        
        .feature-card {
            animation: slideInUp 0.8s ease-out forwards;
            opacity: 0;
            transform: translateY(30px);
        }
        
        .feature-card:nth-child(1) { animation-delay: 0.2s; }
        .feature-card:nth-child(2) { animation-delay: 0.4s; }
        .feature-card:nth-child(3) { animation-delay: 0.6s; }
        .feature-card:nth-child(4) { animation-delay: 0.8s; }
        
        @keyframes slideInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .pulse-ring {
            position: absolute;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: pulseRing 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
        }
        
        .pulse-ring:nth-child(1) { animation-delay: 0s; }
        .pulse-ring:nth-child(2) { animation-delay: 0.5s; }
        .pulse-ring:nth-child(3) { animation-delay: 1s; }
        
        @keyframes pulseRing {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(2.5);
                opacity: 0;
            }
        }
        
        .shimmer {
            position: relative;
            overflow: hidden;
        }
        
        .shimmer::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(
                45deg,
                transparent 30%,
                rgba(255, 255, 255, 0.3) 50%,
                transparent 70%
            );
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-900 via-purple-900 to-slate-800 min-h-screen flex items-center justify-center">
    <div class="splash-container flex flex-col items-center justify-center text-white relative overflow-hidden">
        
        <!-- 背景装饰球体 -->
        <div class="gradient-orb orb-1"></div>
        <div class="gradient-orb orb-2"></div>
        <div class="gradient-orb orb-3"></div>
        
        <!-- 状态栏 -->
        <div class="absolute top-0 left-0 right-0 h-11 flex items-center justify-between px-6 text-white text-sm font-medium z-20">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-three-quarters text-xs"></i>
            </div>
        </div>
        
        <!-- 主要内容 -->
        <div class="flex flex-col items-center justify-center flex-1 px-8 relative z-10">
            
            <!-- Logo图标容器 -->
            <div class="relative mb-8">
                <!-- 脉冲环 -->
                <div class="pulse-ring absolute inset-0 w-32 h-32"></div>
                <div class="pulse-ring absolute inset-0 w-32 h-32"></div>
                <div class="pulse-ring absolute inset-0 w-32 h-32"></div>
                
                <!-- Logo图标 -->
                <div class="logo-animation relative">
                    <div class="w-32 h-32 glass-effect rounded-3xl flex items-center justify-center shadow-2xl shimmer">
                        <i class="fas fa-wallet text-white text-6xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- 应用名称 -->
            <div class="text-center mb-3">
                <h1 class="text-6xl font-bold text-glow mb-2">Money</h1>
                <div class="w-16 h-1 bg-gradient-to-r from-accent to-secondary rounded-full mx-auto"></div>
            </div>
            
            <!-- 应用标语 -->
            <div class="text-center mb-12">
                <p class="text-xl text-white/90 mb-2 font-light">
                    智能记账，理财无忧
                </p>
                <p class="text-lg text-white/70 font-light">
                    Smart Accounting, Easy Finance
                </p>
            </div>
            
            <!-- 功能特点 -->
            <div class="grid grid-cols-2 gap-4 mb-12 w-full max-w-xs">
                <div class="feature-card glass-effect rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-secondary to-primary rounded-xl flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-globe text-white text-xl"></i>
                    </div>
                    <p class="text-sm text-white/90 font-medium">多语言支持</p>
                    <p class="text-xs text-white/60">Global Ready</p>
                </div>
                
                <div class="feature-card glass-effect rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-warning to-accent rounded-xl flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-exchange-alt text-white text-xl"></i>
                    </div>
                    <p class="text-sm text-white/90 font-medium">多货币管理</p>
                    <p class="text-xs text-white/60">Multi Currency</p>
                </div>
                
                <div class="feature-card glass-effect rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-success to-secondary rounded-xl flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <p class="text-sm text-white/90 font-medium">智能分析</p>
                    <p class="text-xs text-white/60">Smart Analytics</p>
                </div>
                
                <div class="feature-card glass-effect rounded-2xl p-4 text-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-danger to-warning rounded-xl flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-shield-alt text-white text-xl"></i>
                    </div>
                    <p class="text-sm text-white/90 font-medium">安全保护</p>
                    <p class="text-xs text-white/60">Secure & Safe</p>
                </div>
            </div>
            
            <!-- 加载动画 -->
            <div class="flex flex-col items-center">
                <div class="loading-animation w-10 h-10 border-3 border-white/30 border-t-white rounded-full mb-4"></div>
                <p class="text-white/80 text-sm font-light">正在加载...</p>
            </div>
            
        </div>
        
        <!-- 底部版权 -->
        <div class="absolute bottom-8 left-0 right-0 text-center z-10">
            <p class="text-white/60 text-xs font-light">© 2024 Money App. All rights reserved.</p>
        </div>
        
        <!-- 顶部装饰线 -->
        <div class="absolute top-12 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full"></div>
        
    </div>

    <script>
        // 启动页面自动跳转逻辑
        setTimeout(() => {
            document.querySelector('.splash-container').style.transform = 'scale(0.95)';
            document.querySelector('.splash-container').style.opacity = '0.8';
        }, 3000);
        
        // 添加交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const logo = document.querySelector('.logo-animation');
            logo.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html> 