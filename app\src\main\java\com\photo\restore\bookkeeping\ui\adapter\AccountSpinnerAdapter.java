package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.enums.AccountType;

import java.text.NumberFormat;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 账户下拉列表适配器
 */
public class AccountSpinnerAdapter extends ArrayAdapter<Account> {

    private final LayoutInflater inflater;
    private List<Account> accounts;
    private final NumberFormat currencyFormat;

    public AccountSpinnerAdapter(@NonNull Context context, @NonNull List<Account> accounts) {
        super(context, R.layout.item_spinner_account, accounts);
        this.inflater = LayoutInflater.from(context);
        this.accounts = accounts;
        
        // 初始化货币格式化器
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        this.currencyFormat.setCurrency(Currency.getInstance("CNY"));
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return createView(position, convertView, parent);
    }

    @Override
    public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return createView(position, convertView, parent);
    }

    private View createView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_spinner_account, parent, false);
            holder = new ViewHolder();
            holder.tvAccountIcon = convertView.findViewById(R.id.tv_account_icon);
            holder.tvAccountName = convertView.findViewById(R.id.tv_account_name);
            holder.tvAccountBalance = convertView.findViewById(R.id.tv_account_balance);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        Account account = accounts.get(position);
        if (account != null) {
            // 设置账户图标
            holder.tvAccountIcon.setText(getAccountTypeEmoji(account.getType()));
            
            // 设置账户名称
            holder.tvAccountName.setText(account.getName());
            
            // 设置账户余额
            if (account.getCurrentBalance() != null) {
                holder.tvAccountBalance.setText(currencyFormat.format(account.getCurrentBalance()));
            } else {
                holder.tvAccountBalance.setText(currencyFormat.format(0));
            }
        }

        return convertView;
    }

    /**
     * 更新账户列表
     */
    public void updateAccounts(List<Account> newAccounts) {
        this.accounts = newAccounts;
        clear();
        addAll(newAccounts);
        notifyDataSetChanged();
    }

    /**
     * 根据账户类型获取对应的emoji
     */
    private String getAccountTypeEmoji(AccountType accountType) {
        if (accountType == null) {
            return "💳";
        }
        
        switch (accountType) {
            case CASH:
                return "💵";
            case SAVINGS_CARD:
                return "🏦";
            case CREDIT_CARD:
                return "💳";
            case ALIPAY:
                return "💙";
            case WECHAT:
                return "💚";
            case E_WALLET:
                return "📱";
            case INVESTMENT:
                return "📈";
            case OTHER:
            default:
                return "💰";
        }
    }

    /**
     * ViewHolder类
     */
    private static class ViewHolder {
        TextView tvAccountIcon;
        TextView tvAccountName;
        TextView tvAccountBalance;
    }
}
