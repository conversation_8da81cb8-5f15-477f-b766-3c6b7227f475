<resources>
    <string name="app_name">Money</string>
    
    <!-- Navigation -->
    <string name="nav_home">Home</string>
    <string name="nav_transactions">Transactions</string>
    <string name="nav_add">Add</string>
    <string name="nav_statistics">Statistics</string>
    <string name="nav_settings">Settings</string>
    
    <!-- Common -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="save">Save</string>
    <string name="delete">Delete</string>
    <string name="edit">Edit</string>
    <string name="add">Add</string>
    <string name="search">Search</string>
    <string name="loading">Loading...</string>
    <string name="no_data">No data</string>
    
    <!-- Home -->
    <string name="total_assets">Total Assets</string>
    <string name="monthly_income">Monthly Income</string>
    <string name="monthly_expense">Monthly Expense</string>
    <string name="recent_transactions">Recent Transactions</string>
    <string name="quick_actions">Quick Actions</string>
    
    <!-- Transaction -->
    <string name="income">Income</string>
    <string name="expense">Expense</string>
    <string name="amount">Amount</string>
    <string name="category">Category</string>
    <string name="account">Account</string>
    <string name="description">Description</string>
    <string name="note">Note</string>
    <string name="date">Date</string>
    <string name="time">Time</string>
    
    <!-- Account -->
    <string name="cash">Cash</string>
    <string name="bank_card">Bank Card</string>
    <string name="credit_card">Credit Card</string>
    <string name="alipay">Alipay</string>
    <string name="wechat_pay">WeChat Pay</string>
    
    <!-- Settings -->
    <string name="language">Language</string>
    <string name="currency">Currency</string>
    <string name="dark_mode">Dark Mode</string>
    <string name="notifications">Notifications</string>
    <string name="biometric_auth">Biometric Auth</string>
    <string name="backup">Backup</string>
    <string name="export">Export</string>
    <string name="about">About</string>

    <!-- Settings Categories -->
    <string name="basic_settings">Basic Settings</string>
    <string name="account_category_management">Accounts &amp; Categories</string>
    <string name="data_management">Data Management</string>
    <string name="about_app">About App</string>
    <string name="account_management">Account Management</string>
    <string name="category_management">Category Management</string>

    <!-- Language and Currency -->
    <string name="select_language">Select Language</string>
    <string name="select_currency">Select Currency</string>
    <string name="chinese">中文</string>
    <string name="english">English</string>
    <string name="currency_cny">Chinese Yuan (CNY)</string>
    <string name="currency_usd">US Dollar (USD)</string>
    <string name="currency_eur">Euro (EUR)</string>
    <string name="currency_jpy">Japanese Yen (JPY)</string>
    <string name="currency_gbp">British Pound (GBP)</string>

    <!-- Theme Settings -->
    <string name="theme">Theme</string>
    <string name="theme_light">Light Mode</string>
    <string name="theme_dark">Dark Mode</string>
    <string name="theme_follow_system">Follow System</string>
    <string name="select_theme">Select Theme</string>

    <!-- Export and Backup -->
    <string name="data_backup">Data Backup</string>
    <string name="data_restore">Data Restore</string>
    <string name="backup_confirm_message">Are you sure you want to backup all data? The backup file will be saved to the app directory.</string>
    <string name="backup_success">Backup successful! File saved to: %s</string>
    <string name="backup_failed">Backup failed: %s</string>
    <string name="restore_confirm_message">Are you sure you want to restore data? This will overwrite all current data.</string>
    <string name="restore_success">Data restore successful!</string>
    <string name="restore_failed">Restore failed: %s</string>
    <string name="backup_file_not_found">Backup file not found</string>
    <string name="invalid_backup_file">Invalid backup file</string>
    <string name="storage_permission_required">Storage permission required for backup operations</string>
    <string name="select_backup_file">Select Backup File</string>
    <string name="no_backup_files">No backup files found</string>
    <string name="delete_backup_file">Delete Backup File</string>
    <string name="backup_file_deleted">Backup file deleted</string>
    <string name="select_export_content">Select Export Content</string>
    <string name="export_transactions">Export Transactions</string>
    <string name="export_accounts">Export Accounts</string>
    <string name="export_categories">Export Categories</string>
    <string name="export_all_data">Export All Data</string>
    <string name="exporting_all_data">Exporting all data...</string>
    <string name="no_transactions_to_export">No transactions to export</string>
    <string name="no_accounts_to_export">No accounts to export</string>
    <string name="no_categories_to_export">No categories to export</string>
    <string name="export_success">Export successful: %s</string>
    <string name="export_failed">Export failed: %s</string>
    <string name="share_failed">Share failed: %s</string>

    <!-- About Dialog -->
    <string name="about_money">About Money</string>
    <string name="about_message">Money Bookkeeping App\n\nVersion: %s\n\nA simple and intuitive personal finance app that helps you manage daily income and expenses,\nanalyze spending habits, and achieve financial goals.\n\nKey Features:\n• Simple and intuitive bookkeeping interface\n• Multiple categories and account management\n• Detailed statistical analysis\n• Data backup and export\n• Multi-language support\n\nThank you for using!</string>

    <!-- Statistics -->
    <string name="statistics_overview">Statistics Overview</string>
    <string name="total_income">Total Income</string>
    <string name="total_expense">Total Expense</string>
    <string name="this_month">This Month</string>
    <string name="this_year">This Year</string>

    <!-- Transaction List -->
    <string name="filter_transactions">Filter Transactions</string>
    <string name="search_transactions">Search Transactions</string>
    <string name="delete_transaction">Delete Transaction</string>
    <string name="edit_transaction">Edit Transaction</string>
    <string name="confirm_delete">Confirm Delete</string>
    <string name="delete_transaction_message">Are you sure you want to delete this transaction?</string>

    <!-- Add Transaction -->
    <string name="add_transaction">Add Transaction</string>
    <string name="select_category">Select Category</string>
    <string name="select_account">Select Account</string>
    <string name="enter_amount">Enter Amount</string>
    <string name="enter_description">Enter Description</string>
    <string name="enter_note">Enter Note</string>
    <string name="transaction_saved">Transaction Saved</string>

    <!-- Error Messages -->
    <string name="error_occurred">Error Occurred</string>
    <string name="network_error">Network Error</string>
    <string name="data_load_failed">Data Load Failed</string>
    <string name="operation_failed">Operation Failed</string>

    <!-- Account Management -->
    <string name="add_account">Add Account</string>
    <string name="edit_account">Edit Account</string>
    <string name="delete_account">Delete Account</string>
    <string name="account_name">Account Name</string>
    <string name="account_type">Account Type</string>
    <string name="initial_balance">Initial Balance</string>
    <string name="current_balance">Current Balance</string>
    <string name="account_name_required">Please enter account name</string>
    <string name="invalid_amount">Please enter a valid amount</string>
    <string name="account_added_successfully">Account added successfully</string>
    <string name="account_updated_successfully">Account updated successfully</string>
    <string name="account_deleted_successfully">Account deleted successfully</string>
    <string name="delete_account_confirmation">Are you sure you want to delete account "%s"?</string>
    <string name="no_accounts_yet">No accounts yet</string>
    <string name="add_first_account_hint">Tap the button below to add your first account</string>
    <string name="initial_balance_format">Initial: %s</string>
    <string name="initial_balance_hint">Set the initial balance for the account, can be 0</string>

    <!-- Category Management -->
    <string name="add_category">Add Category</string>
    <string name="edit_category">Edit Category</string>
    <string name="delete_category">Delete Category</string>
    <string name="category_name">Category Name</string>
    <string name="transaction_type">Transaction Type</string>
    <string name="icon_name">Icon Name</string>
    <string name="color">Color</string>
    <string name="system_default">System Default</string>
    <string name="category_name_required">Please enter category name</string>
    <string name="category_added_successfully">Category added successfully</string>
    <string name="category_updated_successfully">Category updated successfully</string>
    <string name="category_deleted_successfully">Category deleted successfully</string>
    <string name="delete_category_confirmation">Are you sure you want to delete category "%s"?</string>
    <string name="no_categories_yet">No categories yet</string>
    <string name="add_first_category_hint">Tap the button below to add your first category</string>
    <string name="icon_name_hint">e.g.: restaurant, shopping, salary</string>
    <string name="color_format_hint">Format: #RRGGBB, e.g. #FF6B6B</string>

    <!-- Hardcoded text replacements -->
    <string name="language_changed_message">语言已切换为中文，正在重启...</string>
    <string name="language_changed_message_en">Language changed to English. Restarting...</string>
    <string name="default_initial_balance">0.00</string>
    <string name="default_icon_name">default</string>
    <string name="default_color">#FF6B6B</string>
    <string name="arrow_right">></string>
    <string name="default_version">v1.0.0</string>

    <!-- Currency codes -->
    <string name="currency_code_cny">CNY</string>
    <string name="currency_code_usd">USD</string>
    <string name="currency_code_eur">EUR</string>
    <string name="currency_code_jpy">JPY</string>
    <string name="currency_code_gbp">GBP</string>

    <!-- Date format patterns -->
    <string name="date_format_pattern">MMM dd, yyyy</string>
    <string name="time_format_pattern">h:mm a</string>

    <!-- Transaction types -->
    <string name="transaction_type_income">Income</string>
    <string name="transaction_type_expense">Expense</string>
    <string name="transaction_type_transfer">Transfer</string>

    <!-- Add transaction messages -->
    <string name="save_success">Save successful</string>
    <string name="save_failed">Save failed</string>
    <string name="please_enter_amount">Please enter amount</string>
    <string name="amount_must_be_positive">Amount must be greater than 0</string>
    <string name="amount_max_two_decimals">Amount can have at most two decimal places</string>
    <string name="please_enter_valid_amount">Please enter a valid amount</string>
    <string name="please_select_category">Please select category</string>
    <string name="please_select_account">Please select account</string>

    <!-- Transactions fragment -->
    <string name="search_transactions_hint">Search transactions</string>
    <string name="filter_this_month">This Month</string>
    <string name="filter_all">All</string>
    <string name="empty_transactions_icon">📝</string>

    <!-- Home fragment -->
    <string name="default_amount">$0.00</string>

    <!-- Statistics fragment -->
    <string name="filter_month">This Month</string>
    <string name="filter_year">This Year</string>
    <string name="income_expense_overview">Income &amp; Expense Overview</string>
    <string name="expense_distribution">Expense Distribution</string>
    <string name="expense_chart_icon">📊</string>
    <string name="expense_distribution_chart">Expense Distribution Chart</string>
    <string name="category_statistics">Category Statistics</string>
    <string name="net_income">Net Income</string>
    <string name="no_expense_data">No expense data available\nStart adding transactions to see the chart</string>

    <!-- Item layouts -->
    <string name="default_category_icon">🍽️</string>
    <string name="separator_dot">·</string>

    <!-- Transactions fragment operations -->
    <string name="select_operation">Select Operation</string>
    <string name="edit_function_pending">Edit function coming soon</string>
    <string name="transaction_deleted">Transaction deleted</string>
    <string name="select_date_range">Select Date Range</string>
    <string name="select_transaction_type">Select Transaction Type</string>
    <string name="all_types">All</string>
    <string name="export_function_pending">Export function coming soon</string>

    <!-- Adapter default values -->
    <string name="default_category">Default Category</string>
    <string name="default_account">Default Account</string>

    <!-- Add transaction error messages -->
    <string name="category_not_found">Category not found, please select again</string>
    <string name="account_not_found">Account not found, please select again</string>
    <string name="amount_format_error">Invalid amount format</string>
    <string name="save_failed_with_message">Save failed: %s</string>

    <!-- Account types -->
    <string name="account_type_cash">Cash</string>
    <string name="account_type_savings_card">Savings Card</string>
    <string name="account_type_credit_card">Credit Card</string>
    <string name="account_type_alipay">Alipay</string>
    <string name="account_type_wechat">WeChat</string>
    <string name="account_type_e_wallet">E-Wallet</string>
    <string name="account_type_investment">Investment</string>
    <string name="account_type_other">Other</string>

    <!-- Default account name -->
    <string name="default_cash_account">Cash</string>

    <!-- Default category names -->
    <!-- Expense categories -->
    <string name="category_food">Food &amp; Dining</string>
    <string name="category_transport">Transportation</string>
    <string name="category_shopping">Shopping</string>
    <string name="category_entertainment">Entertainment</string>
    <string name="category_health">Healthcare</string>
    <string name="category_education">Education</string>
    <string name="category_housing">Housing</string>
    <string name="category_communication">Communication</string>
    <string name="category_other_expense">Other</string>

    <!-- Income categories -->
    <string name="category_salary">Salary</string>
    <string name="category_bonus">Bonus</string>
    <string name="category_investment">Investment</string>
    <string name="category_part_time">Part-time</string>
    <string name="category_other_income">Other</string>

</resources>
