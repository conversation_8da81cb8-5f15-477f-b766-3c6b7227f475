package com.photo.restore.bookkeeping.data.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.data.dao.TransactionDao;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.utils.PerformanceUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 交易记录仓库类
 */
public class TransactionRepository {

    private final TransactionDao transactionDao;
    private final ExecutorService executor;
    private final LiveData<List<Transaction>> allTransactionsLiveData;

    // 缓存最近查询的结果以提高性能
    private final Map<String, List<Transaction>> queryCache = new HashMap<>();
    private final Map<String, Long> cacheTimestamps = new HashMap<>();
    private static final long CACHE_EXPIRY_TIME = 5 * 60 * 1000; // 5分钟缓存

    public TransactionRepository(Application application) {
        BookkeepingDatabase database = BookkeepingDatabase.getInstance(application);
        transactionDao = database.transactionDao();
        executor = Executors.newFixedThreadPool(2);
        allTransactionsLiveData = transactionDao.getAllTransactionsLiveData();
    }

    /**
     * 获取所有交易记录（LiveData）
     * @return 交易记录列表LiveData
     */
    public LiveData<List<Transaction>> getAllTransactionsLiveData() {
        return allTransactionsLiveData;
    }

    /**
     * 获取最近的交易记录（LiveData）
     * @param limit 限制数量
     * @return 交易记录列表LiveData
     */
    public LiveData<List<Transaction>> getRecentTransactionsLiveData(int limit) {
        return transactionDao.getRecentTransactionsLiveData(limit);
    }

    /**
     * 获取最近的交易记录及其详细信息（LiveData）
     * @param limit 限制数量
     * @return 交易记录详细信息列表LiveData
     */
    public LiveData<List<TransactionWithDetails>> getRecentTransactionsWithDetailsLiveData(int limit) {
        return transactionDao.getRecentTransactionsWithDetailsLiveData(limit);
    }

    /**
     * 根据类型获取交易记录（LiveData）
     * @param type 交易类型
     * @return 交易记录列表LiveData
     */
    public LiveData<List<Transaction>> getTransactionsByTypeLiveData(TransactionType type) {
        return transactionDao.getTransactionsByTypeLiveData(type);
    }

    /**
     * 根据账户获取交易记录（LiveData）
     * @param accountId 账户ID
     * @return 交易记录列表LiveData
     */
    public LiveData<List<Transaction>> getTransactionsByAccountLiveData(String accountId) {
        return transactionDao.getTransactionsByAccountLiveData(accountId);
    }

    /**
     * 根据日期范围获取交易记录（LiveData）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 交易记录列表LiveData
     */
    public LiveData<List<Transaction>> getTransactionsByDateRangeLiveData(Date startDate, Date endDate) {
        return transactionDao.getTransactionsByDateRangeLiveData(startDate.getTime(), endDate.getTime());
    }

    /**
     * 根据ID获取交易记录（LiveData）
     * @param transactionId 交易ID
     * @return 交易记录LiveData
     */
    public LiveData<Transaction> getTransactionByIdLiveData(String transactionId) {
        return transactionDao.getTransactionByIdLiveData(transactionId);
    }

    /**
     * 获取收入总额（LiveData）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 收入总额LiveData
     */
    public LiveData<BigDecimal> getTotalIncomeLiveData(Date startDate, Date endDate) {
        return transactionDao.getTotalIncomeLiveData(startDate.getTime(), endDate.getTime());
    }

    /**
     * 获取支出总额（LiveData）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 支出总额LiveData
     */
    public LiveData<BigDecimal> getTotalExpenseLiveData(Date startDate, Date endDate) {
        return transactionDao.getTotalExpenseLiveData(startDate.getTime(), endDate.getTime());
    }

    /**
     * 获取所有时间收入总额（LiveData）
     * @return 收入总额LiveData
     */
    public LiveData<BigDecimal> getAllTimeIncomeLiveData() {
        return transactionDao.getAllTimeIncomeLiveData();
    }

    /**
     * 获取所有时间支出总额（LiveData）
     * @return 支出总额LiveData
     */
    public LiveData<BigDecimal> getAllTimeExpenseLiveData() {
        return transactionDao.getAllTimeExpenseLiveData();
    }

    /**
     * 获取所有交易记录（同步）
     * @return 交易记录列表
     */
    public List<Transaction> getAllTransactions() {
        return transactionDao.getAllTransactions();
    }

    /**
     * 获取最近的交易记录（同步）
     * @param limit 限制数量
     * @return 交易记录列表
     */
    public List<Transaction> getRecentTransactions(int limit) {
        return transactionDao.getRecentTransactions(limit);
    }

    /**
     * 根据类型获取交易记录（同步）
     * @param type 交易类型
     * @return 交易记录列表
     */
    public List<Transaction> getTransactionsByType(TransactionType type) {
        return transactionDao.getTransactionsByType(type);
    }

    /**
     * 根据账户获取交易记录（同步）
     * @param accountId 账户ID
     * @return 交易记录列表
     */
    public List<Transaction> getTransactionsByAccount(String accountId) {
        return transactionDao.getTransactionsByAccount(accountId);
    }

    /**
     * 根据分类获取交易记录（同步）
     * @param categoryId 分类ID
     * @return 交易记录列表
     */
    public List<Transaction> getTransactionsByCategory(String categoryId) {
        return transactionDao.getTransactionsByCategory(categoryId);
    }

    /**
     * 根据日期范围获取交易记录（同步）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 交易记录列表
     */
    public List<Transaction> getTransactionsByDateRange(Date startDate, Date endDate) {
        return transactionDao.getTransactionsByDateRange(startDate.getTime(), endDate.getTime());
    }

    /**
     * 根据ID获取交易记录（同步）
     * @param transactionId 交易ID
     * @return 交易记录
     */
    public Transaction getTransactionById(String transactionId) {
        return transactionDao.getTransactionById(transactionId);
    }

    /**
     * 获取收入总额（同步）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 收入总额
     */
    public BigDecimal getTotalIncome(Date startDate, Date endDate) {
        return transactionDao.getTotalIncome(startDate.getTime(), endDate.getTime());
    }

    /**
     * 获取支出总额（同步）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 支出总额
     */
    public BigDecimal getTotalExpense(Date startDate, Date endDate) {
        return transactionDao.getTotalExpense(startDate.getTime(), endDate.getTime());
    }

    /**
     * 根据分类统计支出
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 分类支出统计
     */
    public List<TransactionDao.CategoryExpenseStatistic> getExpenseStatisticsByCategory(Date startDate, Date endDate) {
        return transactionDao.getExpenseStatisticsByCategory(startDate.getTime(), endDate.getTime());
    }

    /**
     * 插入交易记录（带性能监控）
     * @param transaction 交易记录
     */
    public void insertTransaction(Transaction transaction) {
        executor.execute(() -> {
            PerformanceUtils.measureDatabaseOperation("insertTransaction", () -> {
                transactionDao.insertTransaction(transaction);
                clearCache(); // 清除缓存
                return null;
            });
        });
    }

    /**
     * 批量插入交易记录（带性能监控）
     * @param transactions 交易记录列表
     */
    public void insertTransactions(List<Transaction> transactions) {
        executor.execute(() -> {
            PerformanceUtils.measureDatabaseOperation("insertTransactions", () -> {
                transactionDao.insertTransactions(transactions);
                clearCache(); // 清除缓存
                return null;
            });
        });
    }

    /**
     * 更新交易记录
     * @param transaction 交易记录
     */
    public void updateTransaction(Transaction transaction) {
        executor.execute(() -> {
            transaction.setUpdatedAt(new Date());
            transactionDao.updateTransaction(transaction);
        });
    }

    /**
     * 删除交易记录
     * @param transaction 交易记录
     */
    public void deleteTransaction(Transaction transaction) {
        executor.execute(() -> transactionDao.deleteTransaction(transaction));
    }

    /**
     * 根据ID删除交易记录
     * @param transactionId 交易ID
     */
    public void deleteTransactionById(String transactionId) {
        executor.execute(() -> transactionDao.deleteTransactionById(transactionId));
    }

    /**
     * 获取交易记录数量
     * @return 交易记录数量
     */
    public int getTransactionCount() {
        return transactionDao.getTransactionCount();
    }

    /**
     * 搜索交易记录
     * @param keyword 关键词
     * @return 交易记录列表
     */
    public List<Transaction> searchTransactions(String keyword) {
        return transactionDao.searchTransactions(keyword);
    }

    /**
     * 获取所有交易记录（带详情）
     */
    public LiveData<List<TransactionWithDetails>> getAllTransactionsWithDetailsLiveData() {
        return transactionDao.getAllTransactionsWithDetailsLiveData();
    }

    /**
     * 根据日期范围获取交易记录（带详情）
     */
    public LiveData<List<TransactionWithDetails>> getTransactionsWithDetailsByDateRangeLiveData(Date startDate, Date endDate) {
        return transactionDao.getTransactionsWithDetailsByDateRangeLiveData(startDate.getTime(), endDate.getTime());
    }

    /**
     * 根据日期范围和类型获取交易记录（带详情）
     */
    public LiveData<List<TransactionWithDetails>> getTransactionsWithDetailsByDateRangeAndTypeLiveData(Date startDate, Date endDate, TransactionType type) {
        return transactionDao.getTransactionsWithDetailsByDateRangeAndTypeLiveData(startDate.getTime(), endDate.getTime(), type);
    }

    /**
     * 清除查询缓存
     */
    private void clearCache() {
        queryCache.clear();
        cacheTimestamps.clear();
    }

    /**
     * 检查缓存是否过期
     */
    private boolean isCacheExpired(String key) {
        Long timestamp = cacheTimestamps.get(key);
        if (timestamp == null) {
            return true;
        }
        return System.currentTimeMillis() - timestamp > CACHE_EXPIRY_TIME;
    }

    /**
     * 获取缓存的查询结果
     */
    private List<Transaction> getCachedResult(String key) {
        if (isCacheExpired(key)) {
            queryCache.remove(key);
            cacheTimestamps.remove(key);
            return null;
        }
        return queryCache.get(key);
    }

    /**
     * 缓存查询结果
     */
    private void cacheResult(String key, List<Transaction> result) {
        queryCache.put(key, result);
        cacheTimestamps.put(key, System.currentTimeMillis());
    }

    /**
     * 清理过期缓存
     */
    public void cleanupExpiredCache() {
        long currentTime = System.currentTimeMillis();
        cacheTimestamps.entrySet().removeIf(entry -> {
            boolean expired = currentTime - entry.getValue() > CACHE_EXPIRY_TIME;
            if (expired) {
                queryCache.remove(entry.getKey());
            }
            return expired;
        });
    }
}
