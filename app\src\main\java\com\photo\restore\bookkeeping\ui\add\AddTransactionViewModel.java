package com.photo.restore.bookkeeping.ui.add;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.photo.restore.bookkeeping.R;

import android.util.Log;

import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.data.repository.AccountRepository;
import com.photo.restore.bookkeeping.data.repository.CategoryRepository;
import com.photo.restore.bookkeeping.data.repository.TransactionRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 添加交易ViewModel
 * 管理添加交易相关的数据和业务逻辑
 */
public class AddTransactionViewModel extends AndroidViewModel {

    private final TransactionRepository transactionRepository;
    private final CategoryRepository categoryRepository;
    private final AccountRepository accountRepository;
    private final ExecutorService executor;

    // 当前交易类型
    private final MutableLiveData<TransactionType> transactionType = new MutableLiveData<>();

    // 保存结果
    private final MutableLiveData<Boolean> saveResult = new MutableLiveData<>();

    // 错误信息
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    
    // 分类和账户列表
    private final LiveData<List<Category>> categories;
    private final LiveData<List<Account>> accounts;

    public AddTransactionViewModel(@NonNull Application application) {
        super(application);

        // 初始化Repository
        transactionRepository = new TransactionRepository(application);
        categoryRepository = new CategoryRepository(application);
        accountRepository = new AccountRepository(application);
        executor = Executors.newSingleThreadExecutor();

        // 获取分类和账户列表
        categories = categoryRepository.getAllActiveCategoriesLiveData();
        accounts = accountRepository.getAllActiveAccountsLiveData();
    }

    /**
     * 获取交易类型
     */
    public LiveData<TransactionType> getTransactionType() {
        return transactionType;
    }

    /**
     * 设置交易类型
     */
    public void setTransactionType(TransactionType type) {
        transactionType.setValue(type);
    }

    /**
     * 获取保存结果
     */
    public LiveData<Boolean> getSaveResult() {
        return saveResult;
    }

    /**
     * 重置保存结果
     */
    public void resetSaveResult() {
        saveResult.setValue(null);
    }

    /**
     * 获取错误信息
     */
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    /**
     * 重置错误信息
     */
    public void resetErrorMessage() {
        errorMessage.setValue(null);
    }

    /**
     * 获取分类列表
     */
    public LiveData<List<Category>> getCategories() {
        return categories;
    }

    /**
     * 根据交易类型获取过滤后的分类列表
     */
    public List<Category> getCategoriesByType(TransactionType type) {
        List<Category> allCategories = categories.getValue();
        if (allCategories == null) {
            return new ArrayList<>();
        }

        List<Category> filteredCategories = new ArrayList<>();
        for (Category category : allCategories) {
            if (category.getType() == type) {
                filteredCategories.add(category);
            }
        }
        return filteredCategories;
    }

    /**
     * 获取账户列表
     */
    public LiveData<List<Account>> getAccounts() {
        return accounts;
    }

    /**
     * 保存交易（使用ID）
     */
    public void saveTransactionWithIds(String amountText, String categoryId, String accountId,
                                      String description, String note) {
        // 在后台线程执行数据库操作
        executor.execute(() -> {
            try {
                // 解析金额
                BigDecimal amount = new BigDecimal(amountText);

                // 获取当前交易类型
                TransactionType type = transactionType.getValue();
                if (type == null) {
                    type = TransactionType.EXPENSE; // 默认为支出
                }

                // 创建交易对象
                Transaction transaction = new Transaction();
                transaction.setId(UUID.randomUUID().toString());
                transaction.setAmount(amount);
                transaction.setType(type);
                transaction.setDescription(description.isEmpty() ? null : description);
                transaction.setNote(note.isEmpty() ? null : note);
                transaction.setDate(new Date());
                transaction.setCreatedAt(new Date());
                transaction.setUpdatedAt(new Date());
                transaction.setCategoryId(categoryId);
                transaction.setAccountId(accountId);

                // 设置默认货币
                transaction.setCurrency("USD");

                // 保存交易
                transactionRepository.insertTransaction(transaction);

                // 设置保存成功
                saveResult.postValue(true);

            } catch (NumberFormatException e) {
                // 金额格式错误
                errorMessage.postValue(getApplication().getString(R.string.amount_format_error));
                saveResult.postValue(false);
            } catch (Exception e) {
                // 其他错误
                errorMessage.postValue(getApplication().getString(R.string.save_failed_with_message, e.getMessage()));
                saveResult.postValue(false);
            }
        });
    }

    /**
     * 保存交易（使用名称，保留向后兼容）
     * @deprecated 使用 saveTransactionWithIds 替代
     */
    @Deprecated
    public void saveTransaction(String amountText, String categoryText, String accountText,
                               String description, String note) {
        // 在后台线程执行数据库操作
        executor.execute(() -> {
            try {
            // 解析金额
            BigDecimal amount = new BigDecimal(amountText);
            
            // 获取当前交易类型
            TransactionType type = transactionType.getValue();
            if (type == null) {
                type = TransactionType.EXPENSE; // 默认为支出
            }
            
            // 创建交易对象
            Transaction transaction = new Transaction();
            transaction.setId(UUID.randomUUID().toString());
            transaction.setAmount(amount);
            transaction.setType(type);
            transaction.setDescription(description.isEmpty() ? null : description);
            transaction.setNote(note.isEmpty() ? null : note);
            transaction.setDate(new Date());
            transaction.setCreatedAt(new Date());
            transaction.setUpdatedAt(new Date());
            
            // 根据分类和账户名称查找对应的ID
            String categoryId = findCategoryIdByName(categoryText, type);
            String accountId = findAccountIdByName(accountText);

            if (categoryId == null) {
                errorMessage.postValue(getApplication().getString(R.string.category_not_found));
                saveResult.postValue(false);
                return;
            }

            if (accountId == null) {
                errorMessage.postValue(getApplication().getString(R.string.account_not_found));
                saveResult.postValue(false);
                return;
            }

            transaction.setCategoryId(categoryId);
            transaction.setAccountId(accountId);
            
            // 设置默认货币
            transaction.setCurrency("USD");

            // 保存交易
            transactionRepository.insertTransaction(transaction);

            // 设置保存成功
            saveResult.postValue(true);

            } catch (NumberFormatException e) {
                // 金额格式错误
                errorMessage.postValue(getApplication().getString(R.string.amount_format_error));
                saveResult.postValue(false);
            } catch (Exception e) {
                // 其他错误
                errorMessage.postValue(getApplication().getString(R.string.save_failed_with_message, e.getMessage()));
                saveResult.postValue(false);
            }
        });
    }

    /**
     * 根据分类名称查找分类ID
     */
    private String findCategoryIdByName(String categoryName, TransactionType type) {
        Log.d("AddTransactionVM", "Looking for category: '" + categoryName + "' with type: " + type);

        // 先获取所有分类来调试
        List<Category> allCategories = categoryRepository.getAllActiveCategories();
        Log.d("AddTransactionVM", "Total active categories: " + allCategories.size());
        for (Category cat : allCategories) {
            Log.d("AddTransactionVM", "Category: '" + cat.getName() + "' type: " + cat.getType() + " id: " + cat.getId());
        }

        Category category = categoryRepository.getCategoryByNameAndType(categoryName, type);
        if (category != null) {
            Log.d("AddTransactionVM", "Found category: " + category.getName() + " with ID: " + category.getId());
            return category.getId();
        } else {
            Log.d("AddTransactionVM", "Category not found for name: '" + categoryName + "' and type: " + type);
            return null;
        }
    }

    /**
     * 根据账户名称查找账户ID
     */
    private String findAccountIdByName(String accountName) {
        Account account = accountRepository.getAccountByName(accountName);
        return account != null ? account.getId() : null;
    }

    /**
     * 验证输入数据
     */
    public boolean validateInput(String amountText, String categoryText, String accountText) {
        // 验证金额
        if (amountText == null || amountText.trim().isEmpty()) {
            return false;
        }
        
        try {
            BigDecimal amount = new BigDecimal(amountText);
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
        } catch (NumberFormatException e) {
            return false;
        }
        
        // 验证分类
        if (categoryText == null || categoryText.trim().isEmpty()) {
            return false;
        }
        
        // 验证账户
        if (accountText == null || accountText.trim().isEmpty()) {
            return false;
        }
        
        return true;
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
