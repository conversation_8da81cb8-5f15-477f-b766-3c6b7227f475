package com.photo.restore.bookkeeping.ui.settings;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.RadioGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.google.android.material.tabs.TabLayout;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.ui.adapter.CategoryManagementAdapter;

import java.util.ArrayList;

/**
 * 分类管理Fragment
 */
public class CategoryManagementFragment extends Fragment {

    private CategoryManagementViewModel viewModel;
    
    // UI组件
    private MaterialToolbar toolbar;
    private TabLayout tabLayout;
    private RecyclerView rvCategories;
    private FloatingActionButton fabAddCategory;
    
    // 适配器
    private CategoryManagementAdapter categoryAdapter;
    
    // 当前选中的类型
    private TransactionType currentType = TransactionType.EXPENSE;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_category_management, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        initViewModel();
        setupTabs();
        setupRecyclerView();
        observeData();
    }

    /**
     * 初始化视图
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        tabLayout = view.findViewById(R.id.tab_layout);
        rvCategories = view.findViewById(R.id.rv_categories);
        fabAddCategory = view.findViewById(R.id.fab_add_category);

        // 设置返回按钮
        toolbar.setNavigationOnClickListener(v -> {
            Navigation.findNavController(view).navigateUp();
        });

        fabAddCategory.setOnClickListener(v -> showAddCategoryDialog());
    }

    /**
     * 初始化ViewModel
     */
    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(CategoryManagementViewModel.class);
    }

    /**
     * 设置标签页
     */
    private void setupTabs() {
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.expense)));
        tabLayout.addTab(tabLayout.newTab().setText(getString(R.string.income)));
        
        tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                currentType = tab.getPosition() == 0 ? TransactionType.EXPENSE : TransactionType.INCOME;
                loadCategoriesByType();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        categoryAdapter = new CategoryManagementAdapter(new ArrayList<>(), new CategoryManagementAdapter.OnCategoryActionListener() {
            @Override
            public void onEditCategory(Category category) {
                showEditCategoryDialog(category);
            }

            @Override
            public void onDeleteCategory(Category category) {
                showDeleteCategoryDialog(category);
            }

            @Override
            public void onToggleCategoryStatus(Category category) {
                viewModel.toggleCategoryStatus(category);
            }
        });

        rvCategories.setLayoutManager(new LinearLayoutManager(getContext()));
        rvCategories.setAdapter(categoryAdapter);
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        viewModel.getCategoriesByType(currentType).observe(getViewLifecycleOwner(), categories -> {
            if (categories != null) {
                categoryAdapter.updateCategories(categories);
            }
        });
    }

    /**
     * 根据类型加载分类
     */
    private void loadCategoriesByType() {
        // 移除之前的观察者，避免重复观察
        viewModel.getCategoriesByType(currentType).removeObservers(getViewLifecycleOwner());

        // 重新观察新类型的数据
        viewModel.getCategoriesByType(currentType).observe(getViewLifecycleOwner(), categories -> {
            if (categories != null) {
                categoryAdapter.updateCategories(categories);
            }
        });
    }

    /**
     * 显示添加分类对话框
     */
    private void showAddCategoryDialog() {
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_category, null);
        
        EditText etCategoryName = dialogView.findViewById(R.id.et_category_name);
        RadioGroup rgTransactionType = dialogView.findViewById(R.id.rg_transaction_type);
        EditText etIconName = dialogView.findViewById(R.id.et_icon_name);
        EditText etColor = dialogView.findViewById(R.id.et_color);
        
        // 根据当前选中的标签页设置默认类型
        if (currentType == TransactionType.EXPENSE) {
            rgTransactionType.check(R.id.rb_expense);
        } else {
            rgTransactionType.check(R.id.rb_income);
        }
        
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.add_category))
                .setView(dialogView)
                .setPositiveButton(getString(R.string.save), (dialog, which) -> {
                    String name = etCategoryName.getText().toString().trim();
                    String iconName = etIconName.getText().toString().trim();
                    String color = etColor.getText().toString().trim();
                    
                    if (name.isEmpty()) {
                        Toast.makeText(getContext(), getString(R.string.category_name_required), Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    TransactionType type = rgTransactionType.getCheckedRadioButtonId() == R.id.rb_expense 
                            ? TransactionType.EXPENSE : TransactionType.INCOME;
                    
                    Category newCategory = new Category();
                    newCategory.setName(name);
                    newCategory.setType(type);
                    newCategory.setIconName(iconName.isEmpty() ? "default" : iconName);
                    newCategory.setColor(color.isEmpty() ? "#FF6B6B" : color);
                    newCategory.setSystemDefault(false);
                    newCategory.setActive(true);
                    
                    viewModel.addCategory(newCategory);
                    Toast.makeText(getContext(), getString(R.string.category_added_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示编辑分类对话框
     */
    private void showEditCategoryDialog(Category category) {
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_category, null);
        
        EditText etCategoryName = dialogView.findViewById(R.id.et_category_name);
        RadioGroup rgTransactionType = dialogView.findViewById(R.id.rg_transaction_type);
        EditText etIconName = dialogView.findViewById(R.id.et_icon_name);
        EditText etColor = dialogView.findViewById(R.id.et_color);
        
        // 填充现有数据
        etCategoryName.setText(category.getName());
        etIconName.setText(category.getIconName());
        etColor.setText(category.getColor());
        
        if (category.getType() == TransactionType.EXPENSE) {
            rgTransactionType.check(R.id.rb_expense);
        } else {
            rgTransactionType.check(R.id.rb_income);
        }
        
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.edit_category))
                .setView(dialogView)
                .setPositiveButton(getString(R.string.save), (dialog, which) -> {
                    String name = etCategoryName.getText().toString().trim();
                    String iconName = etIconName.getText().toString().trim();
                    String color = etColor.getText().toString().trim();
                    
                    if (name.isEmpty()) {
                        Toast.makeText(getContext(), getString(R.string.category_name_required), Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    TransactionType type = rgTransactionType.getCheckedRadioButtonId() == R.id.rb_expense 
                            ? TransactionType.EXPENSE : TransactionType.INCOME;
                    
                    category.setName(name);
                    category.setType(type);
                    category.setIconName(iconName.isEmpty() ? "default" : iconName);
                    category.setColor(color.isEmpty() ? "#FF6B6B" : color);
                    
                    viewModel.updateCategory(category);
                    Toast.makeText(getContext(), getString(R.string.category_updated_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示删除分类确认对话框
     */
    private void showDeleteCategoryDialog(Category category) {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.delete_category))
                .setMessage(getString(R.string.delete_category_confirmation, category.getName()))
                .setPositiveButton(getString(R.string.delete), (dialog, which) -> {
                    viewModel.deleteCategory(category);
                    Toast.makeText(getContext(), getString(R.string.category_deleted_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }
}
