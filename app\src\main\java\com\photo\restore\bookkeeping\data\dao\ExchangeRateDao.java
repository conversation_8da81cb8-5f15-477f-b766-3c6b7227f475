package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.ExchangeRate;

import java.math.BigDecimal;
import java.util.List;

/**
 * 汇率数据访问对象
 */
@Dao
public interface ExchangeRateDao {

    /**
     * 获取所有汇率（LiveData）
     * @return 汇率列表LiveData
     */
    @Query("SELECT * FROM exchange_rates ORDER BY last_updated DESC")
    LiveData<List<ExchangeRate>> getAllExchangeRatesLiveData();

    /**
     * 获取所有汇率（同步）
     * @return 汇率列表
     */
    @Query("SELECT * FROM exchange_rates ORDER BY last_updated DESC")
    List<ExchangeRate> getAllExchangeRates();

    /**
     * 根据货币对获取汇率
     * @param fromCurrency 源货币
     * @param toCurrency 目标货币
     * @return 汇率
     */
    @Query("SELECT * FROM exchange_rates WHERE from_currency = :fromCurrency AND to_currency = :toCurrency")
    ExchangeRate getExchangeRate(String fromCurrency, String toCurrency);

    /**
     * 根据货币对获取汇率（LiveData）
     * @param fromCurrency 源货币
     * @param toCurrency 目标货币
     * @return 汇率LiveData
     */
    @Query("SELECT * FROM exchange_rates WHERE from_currency = :fromCurrency AND to_currency = :toCurrency")
    LiveData<ExchangeRate> getExchangeRateLiveData(String fromCurrency, String toCurrency);

    /**
     * 根据源货币获取所有汇率
     * @param fromCurrency 源货币
     * @return 汇率列表
     */
    @Query("SELECT * FROM exchange_rates WHERE from_currency = :fromCurrency ORDER BY to_currency ASC")
    List<ExchangeRate> getExchangeRatesFromCurrency(String fromCurrency);

    /**
     * 根据目标货币获取所有汇率
     * @param toCurrency 目标货币
     * @return 汇率列表
     */
    @Query("SELECT * FROM exchange_rates WHERE to_currency = :toCurrency ORDER BY from_currency ASC")
    List<ExchangeRate> getExchangeRatesToCurrency(String toCurrency);

    /**
     * 根据ID获取汇率
     * @param rateId 汇率ID
     * @return 汇率
     */
    @Query("SELECT * FROM exchange_rates WHERE id = :rateId")
    ExchangeRate getExchangeRateById(String rateId);

    /**
     * 获取过期的汇率（超过24小时）
     * @param expiredTime 过期时间戳
     * @return 过期汇率列表
     */
    @Query("SELECT * FROM exchange_rates WHERE last_updated < :expiredTime")
    List<ExchangeRate> getExpiredExchangeRates(long expiredTime);

    /**
     * 获取最新更新的汇率
     * @param limit 限制数量
     * @return 汇率列表
     */
    @Query("SELECT * FROM exchange_rates ORDER BY last_updated DESC LIMIT :limit")
    List<ExchangeRate> getLatestExchangeRates(int limit);

    /**
     * 插入汇率
     * @param exchangeRate 汇率
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertExchangeRate(ExchangeRate exchangeRate);

    /**
     * 批量插入汇率
     * @param exchangeRates 汇率列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertExchangeRates(List<ExchangeRate> exchangeRates);

    /**
     * 更新汇率
     * @param exchangeRate 汇率
     */
    @Update
    void updateExchangeRate(ExchangeRate exchangeRate);

    /**
     * 更新汇率值
     * @param fromCurrency 源货币
     * @param toCurrency 目标货币
     * @param rate 汇率值
     * @param lastUpdated 更新时间
     */
    @Query("UPDATE exchange_rates SET rate = :rate, last_updated = :lastUpdated WHERE from_currency = :fromCurrency AND to_currency = :toCurrency")
    void updateExchangeRateValue(String fromCurrency, String toCurrency, BigDecimal rate, long lastUpdated);

    /**
     * 删除汇率
     * @param exchangeRate 汇率
     */
    @Delete
    void deleteExchangeRate(ExchangeRate exchangeRate);

    /**
     * 根据货币对删除汇率
     * @param fromCurrency 源货币
     * @param toCurrency 目标货币
     */
    @Query("DELETE FROM exchange_rates WHERE from_currency = :fromCurrency AND to_currency = :toCurrency")
    void deleteExchangeRateByCurrencyPair(String fromCurrency, String toCurrency);

    /**
     * 删除过期的汇率
     * @param expiredTime 过期时间戳
     */
    @Query("DELETE FROM exchange_rates WHERE last_updated < :expiredTime")
    void deleteExpiredExchangeRates(long expiredTime);

    /**
     * 获取汇率数量
     * @return 汇率数量
     */
    @Query("SELECT COUNT(*) FROM exchange_rates")
    int getExchangeRateCount();

    /**
     * 检查汇率是否存在
     * @param fromCurrency 源货币
     * @param toCurrency 目标货币
     * @return 存在返回1，不存在返回0
     */
    @Query("SELECT COUNT(*) FROM exchange_rates WHERE from_currency = :fromCurrency AND to_currency = :toCurrency")
    int checkExchangeRateExists(String fromCurrency, String toCurrency);

    /**
     * 获取所有涉及的货币
     * @return 货币列表
     */
    @Query("SELECT DISTINCT from_currency FROM exchange_rates UNION SELECT DISTINCT to_currency FROM exchange_rates ORDER BY from_currency ASC")
    List<String> getAllCurrencies();
}
