package com.photo.restore.bookkeeping.data.entity;

import android.content.Context;
import androidx.room.Embedded;
import androidx.room.Relation;

/**
 * 包含关联数据的交易记录类
 * 用于在UI中显示完整的交易信息，包括分类和账户名称
 */
public class TransactionWithDetails {
    
    @Embedded
    public Transaction transaction;
    
    @Relation(
        parentColumn = "category_id",
        entityColumn = "id"
    )
    public Category category;
    
    @Relation(
        parentColumn = "account_id", 
        entityColumn = "id"
    )
    public Account account;

    // 构造函数
    public TransactionWithDetails() {
    }

    public TransactionWithDetails(Transaction transaction, Category category, Account account) {
        this.transaction = transaction;
        this.category = category;
        this.account = account;
    }

    // Getter和Setter方法
    public Transaction getTransaction() {
        return transaction;
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
    }

    public Category getCategory() {
        return category;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }

    /**
     * 获取分类名称
     * @return 分类名称，如果分类为空则返回默认值
     */
    public String getCategoryName() {
        return category != null ? category.getName() : "未分类";
    }

    /**
     * 获取分类图标
     * @return 分类图标名称，如果分类为空则返回默认值
     */
    public String getCategoryIcon() {
        return category != null ? category.getIconName() : "default";
    }

    /**
     * 获取分类颜色
     * @return 分类颜色，如果分类为空则返回默认值
     */
    public String getCategoryColor() {
        return category != null ? category.getColor() : "#666666";
    }

    /**
     * 获取账户名称
     * @return 账户名称，如果账户为空则返回默认值
     */
    public String getAccountName() {
        return account != null ? account.getName() : "未知账户";
    }

    /**
     * 获取账户类型显示名称
     * @param context 上下文
     * @return 账户类型显示名称
     */
    public String getAccountTypeDisplayName(Context context) {
        return account != null ? account.getType().getDisplayName(context) : "";
    }

    /**
     * 获取账户类型显示名称（保持向后兼容）
     * @param isEnglish 是否为英文
     * @return 账户类型显示名称
     * @deprecated 使用 getAccountTypeDisplayName(Context context) 替代
     */
    @Deprecated
    public String getAccountTypeDisplayName(boolean isEnglish) {
        return account != null ? account.getType().getDisplayName(isEnglish) : "";
    }
}
