package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.util.List;

/**
 * 分类数据访问对象
 */
@Dao
public interface CategoryDao {

    /**
     * 获取所有活跃分类（LiveData）
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @return 分类列表LiveData
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 ORDER BY is_system_default ASC, created_at DESC")
    LiveData<List<Category>> getAllActiveCategoriesLiveData();

    /**
     * 获取所有活跃分类（同步）
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @return 分类列表
     */
    @Query("SELECT * FROM categories WHERE is_active = 1 ORDER BY is_system_default ASC, created_at DESC")
    List<Category> getAllActiveCategories();

    /**
     * 获取所有分类（包括非活跃）（LiveData）
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @return 分类列表LiveData
     */
    @Query("SELECT * FROM categories ORDER BY is_system_default ASC, created_at DESC")
    LiveData<List<Category>> getAllCategoriesLiveData();

    /**
     * 根据类型获取分类
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @param type 交易类型
     * @return 分类列表
     */
    @Query("SELECT * FROM categories WHERE type = :type AND is_active = 1 ORDER BY is_system_default ASC, created_at DESC")
    List<Category> getCategoriesByType(TransactionType type);

    /**
     * 根据类型获取分类（LiveData）
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @param type 交易类型
     * @return 分类列表LiveData
     */
    @Query("SELECT * FROM categories WHERE type = :type AND is_active = 1 ORDER BY is_system_default ASC, created_at DESC")
    LiveData<List<Category>> getCategoriesByTypeLiveData(TransactionType type);

    /**
     * 根据类型获取所有分类（包括非活跃）（LiveData）- 用于管理界面
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @param type 交易类型
     * @return 分类列表LiveData
     */
    @Query("SELECT * FROM categories WHERE type = :type ORDER BY is_system_default ASC, created_at DESC")
    LiveData<List<Category>> getAllCategoriesByTypeLiveData(TransactionType type);

    /**
     * 获取顶级分类
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @param type 交易类型
     * @return 顶级分类列表
     */
    @Query("SELECT * FROM categories WHERE type = :type AND is_active = 1 AND (parent_category_id IS NULL OR parent_category_id = '') ORDER BY is_system_default ASC, created_at DESC")
    List<Category> getTopLevelCategories(TransactionType type);

    /**
     * 获取顶级分类（LiveData）
     * 用户自定义分类在前（按创建时间降序），系统默认分类在后（按创建时间升序）
     * @param type 交易类型
     * @return 顶级分类列表LiveData
     */
    @Query("SELECT * FROM categories WHERE type = :type AND is_active = 1 AND (parent_category_id IS NULL OR parent_category_id = '') ORDER BY is_system_default ASC, created_at DESC")
    LiveData<List<Category>> getTopLevelCategoriesLiveData(TransactionType type);

    /**
     * 获取子分类
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    @Query("SELECT * FROM categories WHERE parent_category_id = :parentId AND is_active = 1 ORDER BY created_at DESC")
    List<Category> getSubCategories(String parentId);

    /**
     * 获取子分类（LiveData）
     * @param parentId 父分类ID
     * @return 子分类列表LiveData
     */
    @Query("SELECT * FROM categories WHERE parent_category_id = :parentId AND is_active = 1 ORDER BY created_at DESC")
    LiveData<List<Category>> getSubCategoriesLiveData(String parentId);

    /**
     * 根据ID获取分类
     * @param categoryId 分类ID
     * @return 分类
     */
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    Category getCategoryById(String categoryId);

    /**
     * 根据ID获取分类（LiveData）
     * @param categoryId 分类ID
     * @return 分类LiveData
     */
    @Query("SELECT * FROM categories WHERE id = :categoryId")
    LiveData<Category> getCategoryByIdLiveData(String categoryId);

    /**
     * 获取系统默认分类
     * @return 系统默认分类列表
     */
    @Query("SELECT * FROM categories WHERE is_system_default = 1 AND is_active = 1 ORDER BY created_at ASC")
    List<Category> getSystemDefaultCategories();

    /**
     * 根据名称搜索分类
     * @param name 分类名称
     * @return 分类列表
     */
    @Query("SELECT * FROM categories WHERE name LIKE '%' || :name || '%' AND is_active = 1 ORDER BY created_at DESC")
    List<Category> searchCategoriesByName(String name);

    /**
     * 插入分类
     * @param category 分类
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertCategory(Category category);

    /**
     * 批量插入分类
     * @param categories 分类列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertCategories(List<Category> categories);

    /**
     * 更新分类
     * @param category 分类
     */
    @Update
    void updateCategory(Category category);

    /**
     * 软删除分类（设置为非活跃）
     * @param categoryId 分类ID
     */
    @Query("UPDATE categories SET is_active = 0 WHERE id = :categoryId")
    void softDeleteCategory(String categoryId);

    /**
     * 删除分类
     * @param category 分类
     */
    @Delete
    void deleteCategory(Category category);

    /**
     * 获取分类数量
     * @param type 交易类型
     * @return 分类数量
     */
    @Query("SELECT COUNT(*) FROM categories WHERE type = :type AND is_active = 1")
    int getCategoryCount(TransactionType type);

    /**
     * 检查分类名称是否存在
     * @param name 分类名称
     * @param type 交易类型
     * @param excludeId 排除的分类ID（用于编辑时检查）
     * @return 存在返回1，不存在返回0
     */
    @Query("SELECT COUNT(*) FROM categories WHERE name = :name AND type = :type AND is_active = 1 AND id != :excludeId")
    int checkCategoryNameExists(String name, TransactionType type, String excludeId);

    /**
     * 根据名称查找分类
     * @param name 分类名称
     * @return 分类对象
     */
    @Query("SELECT * FROM categories WHERE name = :name AND is_active = 1 LIMIT 1")
    Category getCategoryByName(String name);

    /**
     * 根据名称和类型查找分类
     * @param name 分类名称
     * @param type 交易类型
     * @return 分类对象
     */
    @Query("SELECT * FROM categories WHERE name = :name AND type = :type AND is_active = 1 LIMIT 1")
    Category getCategoryByNameAndType(String name, TransactionType type);
}
