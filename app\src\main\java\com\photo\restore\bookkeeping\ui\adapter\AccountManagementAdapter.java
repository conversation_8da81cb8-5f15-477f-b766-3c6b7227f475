package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Account;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 账户管理适配器
 */
public class AccountManagementAdapter extends RecyclerView.Adapter<AccountManagementAdapter.AccountViewHolder> {

    private List<Account> accounts;
    private final OnAccountActionListener listener;
    private final NumberFormat currencyFormat;

    /**
     * 账户操作监听器接口
     */
    public interface OnAccountActionListener {
        void onEditAccount(Account account);
        void onDeleteAccount(Account account);
        void onToggleAccountStatus(Account account);
    }

    public AccountManagementAdapter(List<Account> accounts, OnAccountActionListener listener) {
        this.accounts = accounts;
        this.listener = listener;
        
        // 初始化货币格式化器
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
        this.currencyFormat.setCurrency(Currency.getInstance("USD"));
    }

    @NonNull
    @Override
    public AccountViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_account_management, parent, false);
        return new AccountViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull AccountViewHolder holder, int position) {
        Account account = accounts.get(position);
        Context context = holder.itemView.getContext();
        
        // 设置账户名称
        holder.tvAccountName.setText(account.getName());
        
        // 设置账户类型
        holder.tvAccountType.setText(account.getType().getDisplayName(context));
        
        // 设置当前余额
        holder.tvCurrentBalance.setText(currencyFormat.format(account.getCurrentBalance()));
        
        // 设置初始余额
        holder.tvInitialBalance.setText(context.getString(R.string.initial_balance_format, 
                currencyFormat.format(account.getInitialBalance())));
        
        // 设置账户状态
        holder.switchActive.setChecked(account.isActive());
        holder.switchActive.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (listener != null) {
                listener.onToggleAccountStatus(account);
            }
        });
        
        // 设置余额颜色
        if (account.getCurrentBalance().compareTo(BigDecimal.ZERO) >= 0) {
            holder.tvCurrentBalance.setTextColor(ContextCompat.getColor(context, R.color.income_color));
        } else {
            holder.tvCurrentBalance.setTextColor(ContextCompat.getColor(context, R.color.expense_color));
        }
        
        // 设置编辑按钮
        holder.btnEdit.setOnClickListener(v -> {
            if (listener != null) {
                listener.onEditAccount(account);
            }
        });
        
        // 设置删除按钮
        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null) {
                listener.onDeleteAccount(account);
            }
        });
        
        // 设置项目点击
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onEditAccount(account);
            }
        });
    }

    @Override
    public int getItemCount() {
        return accounts.size();
    }

    /**
     * 更新账户列表
     */
    public void updateAccounts(List<Account> newAccounts) {
        this.accounts = newAccounts;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    static class AccountViewHolder extends RecyclerView.ViewHolder {
        TextView tvAccountName;
        TextView tvAccountType;
        TextView tvCurrentBalance;
        TextView tvInitialBalance;
        Switch switchActive;
        ImageButton btnEdit;
        ImageButton btnDelete;

        public AccountViewHolder(@NonNull View itemView) {
            super(itemView);
            tvAccountName = itemView.findViewById(R.id.tv_account_name);
            tvAccountType = itemView.findViewById(R.id.tv_account_type);
            tvCurrentBalance = itemView.findViewById(R.id.tv_current_balance);
            tvInitialBalance = itemView.findViewById(R.id.tv_initial_balance);
            switchActive = itemView.findViewById(R.id.switch_active);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }
    }
}
