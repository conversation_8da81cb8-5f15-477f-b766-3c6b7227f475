<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 设置</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5B4BCF',
                        'secondary': '#A29BFE',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E84393',
                        'surface': '#FFFFFF',
                        'neutral-50': '#FAFBFC',
                        'neutral-100': '#F4F5F7',
                        'neutral-200': '#E3E5E8',
                        'neutral-300': '#CFD3D8',
                        'neutral-400': '#A6ACB5',
                        'neutral-500': '#7B8794',
                        'neutral-600': '#5A6575',
                        'neutral-700': '#3E4651',
                        'neutral-800': '#2D3843',
                        'neutral-900': '#1F2937'
                    },
                    fontFamily: {
                        'display': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'system-ui', 'sans-serif'],
                        'body': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'system-ui', 'sans-serif']
                    },
                    borderRadius: {
                        '2xl': '1rem',
                        '3xl': '1.5rem',
                        '4xl': '2rem'
                    },
                    boxShadow: {
                        'soft': '0 2px 12px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 24px rgba(0, 0, 0, 0.06)',
                        'large': '0 8px 40px rgba(0, 0, 0, 0.08)',
                        'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 16px rgba(0, 0, 0, 0.04)',
                        'button': '0 2px 8px rgba(108, 92, 231, 0.2)',
                        'floating': '0 8px 32px rgba(108, 92, 231, 0.15)',
                        'glow': '0 0 20px rgba(108, 92, 231, 0.3)'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(145deg, #F8F9FA 0%, #E9ECEF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        .glass-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .profile-card {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 50%, #FD79A8 100%);
            position: relative;
            overflow: hidden;
        }
        
        .profile-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .settings-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .settings-item:hover {
            transform: translateY(-2px) scale(1.01);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-color: rgba(108, 92, 231, 0.2);
        }
        
        .settings-item:active {
            transform: scale(0.98);
            transition: all 0.1s;
        }
        
        .icon-container {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
        }
        
        .icon-container:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);
        }
        
        .toggle-switch {
            width: 56px;
            height: 34px;
            background: linear-gradient(145deg, #E3E5E8 0%, #CFD3D8 100%);
            border-radius: 17px;
            position: relative;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(108, 92, 231, 0.1);
        }
        
        .toggle-switch.active {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
        }
        
        .toggle-slider {
            width: 30px;
            height: 30px;
            background: linear-gradient(145deg, #FFFFFF 0%, #F8F9FA 100%);
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(22px);
            box-shadow: 0 2px 12px rgba(0,0,0,0.2);
        }
        
        .section-card {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .section-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
        }
        
        .floating-action-mini {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
            transition: all 0.3s ease;
        }
        
        .floating-action-mini:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(108, 92, 231, 0.4);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-neutral-50 to-neutral-100 min-h-screen flex items-center justify-center font-body">
    <div class="phone-container">
        
        <!-- 状态栏 -->
        <div class="h-11 bg-transparent flex items-center justify-between px-6 text-neutral-800 text-sm font-medium">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-three-quarters text-xs"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="glass-card h-16 flex items-center justify-between px-4 mb-2 mx-4 rounded-2xl shadow-card">
            <h1 class="font-bold text-xl text-neutral-800 font-display">个人设置</h1>
            <button class="floating-action-mini p-2 rounded-xl">
                <i class="fas fa-user-circle text-white text-xl"></i>
            </button>
        </div>
        
        <!-- 用户信息 -->
        <div class="profile-card mx-4 mb-4 px-6 py-8 rounded-3xl shadow-floating relative">
            <div class="relative z-10 flex items-center">
                <div class="w-20 h-20 bg-gradient-to-br from-white/20 to-white/10 rounded-3xl flex items-center justify-center mr-5 backdrop-blur-sm border border-white/20 shadow-lg">
                    <span class="text-white text-3xl font-bold font-display">李</span>
                </div>
                <div class="flex-1">
                    <h3 class="font-bold text-xl text-white font-display">李先生</h3>
                    <p class="text-sm text-white/80 font-medium mb-3">记账达人 · 已使用 128 天</p>
                    <div class="flex items-center">
                        <div class="flex mr-3">
                            <i class="fas fa-star text-warning text-sm"></i>
                            <i class="fas fa-star text-warning text-sm"></i>
                            <i class="fas fa-star text-warning text-sm"></i>
                            <i class="fas fa-star text-warning text-sm"></i>
                            <i class="fas fa-star text-white/30 text-sm"></i>
                        </div>
                        <span class="text-xs text-white/70 font-medium">记账等级 4</span>
                    </div>
                </div>
                <div class="glass-card p-2 rounded-xl border border-white/20">
                    <i class="fas fa-chevron-right text-white text-sm"></i>
                </div>
            </div>
        </div>
        
        <!-- 设置列表 -->
        <div class="flex-1 overflow-y-auto px-4 space-y-6 pb-24">
            
            <!-- 账户管理 -->
            <div class="section-card rounded-3xl p-6 shadow-card">
                <h4 class="font-bold text-lg font-display text-neutral-800 mb-6">账户管理</h4>
                <div class="space-y-4">
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center">
                        <div class="icon-container w-12 h-12 from-primary to-primary-dark rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-credit-card text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                            <p class="font-bold text-neutral-800">我的账户</p>
                            <p class="text-xs text-neutral-500 font-medium">管理银行卡、现金等账户</p>
                        </div>
                        <div class="glass-card p-2 rounded-xl">
                            <i class="fas fa-chevron-right text-neutral-400 text-xs"></i>
                        </div>
                    </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center">
                        <div class="icon-container w-12 h-12 from-success to-emerald-500 rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-list text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                            <p class="font-bold text-neutral-800">分类管理</p>
                            <p class="text-xs text-neutral-500 font-medium">自定义收支分类</p>
                        </div>
                        <div class="glass-card p-2 rounded-xl">
                            <i class="fas fa-chevron-right text-neutral-400 text-xs"></i>
                        </div>
                    </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center">
                        <div class="icon-container w-12 h-12 from-warning to-orange-400 rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-bullseye text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                            <p class="font-bold text-neutral-800">预算设置</p>
                            <p class="text-xs text-neutral-500 font-medium">设置月度预算目标</p>
                        </div>
                        <div class="glass-card p-2 rounded-xl">
                            <i class="fas fa-chevron-right text-neutral-400 text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 应用设置 -->
            <div class="section-card rounded-3xl p-6 shadow-card">
                <h4 class="font-bold text-lg font-display text-neutral-800 mb-6">应用设置</h4>
                <div class="space-y-4">
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center">
                        <div class="icon-container w-12 h-12 from-secondary to-accent rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-globe text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                            <p class="font-bold text-neutral-800">语言设置</p>
                            <p class="text-xs text-neutral-500 font-medium">简体中文</p>
                        </div>
                        <div class="glass-card p-2 rounded-xl">
                            <i class="fas fa-chevron-right text-neutral-400 text-xs"></i>
                        </div>
                    </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center">
                        <div class="icon-container w-12 h-12 from-danger to-pink-400 rounded-2xl flex items-center justify-center mr-4">
                        <i class="fas fa-dollar-sign text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                            <p class="font-bold text-neutral-800">默认货币</p>
                            <p class="text-xs text-neutral-500 font-medium">人民币 (¥)</p>
                        </div>
                        <div class="glass-card p-2 rounded-xl">
                            <i class="fas fa-chevron-right text-neutral-400 text-xs"></i>
                        </div>
                    </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center justify-between">
                    <div class="flex items-center">
                            <div class="icon-container w-12 h-12 from-neutral-600 to-neutral-700 rounded-2xl flex items-center justify-center mr-4">
                            <i class="fas fa-moon text-white text-sm"></i>
                        </div>
                        <div>
                                <p class="font-bold text-neutral-800">深色模式</p>
                                <p class="text-xs text-neutral-500 font-medium">跟随系统</p>
                        </div>
                    </div>
                    <div class="toggle-switch" id="darkModeToggle">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center justify-between">
                    <div class="flex items-center">
                            <div class="icon-container w-12 h-12 from-primary to-secondary rounded-2xl flex items-center justify-center mr-4">
                            <i class="fas fa-fingerprint text-white text-sm"></i>
                        </div>
                        <div>
                                <p class="font-bold text-neutral-800">生物识别</p>
                                <p class="text-xs text-neutral-500 font-medium">Face ID 解锁</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" id="biometricToggle">
                        <div class="toggle-slider"></div>
                    </div>
                </div>
                    <div class="settings-item px-5 py-4 rounded-2xl flex items-center justify-between">
                    <div class="flex items-center">
                            <div class="icon-container w-12 h-12 from-success to-emerald-400 rounded-2xl flex items-center justify-center mr-4">
                            <i class="fas fa-bell text-white text-sm"></i>
                        </div>
                        <div>
                                <p class="font-bold text-neutral-800">推送通知</p>
                                <p class="text-xs text-neutral-500 font-medium">预算提醒等</p>
                        </div>
                    </div>
                    <div class="toggle-switch active" id="notificationToggle">
                        <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 数据管理 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                <h4 class="px-4 py-3 text-sm font-semibold text-ios-gray border-b border-ios-gray-5">数据管理</h4>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cloud text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">iCloud 同步</p>
                        <p class="text-xs text-ios-gray">最后同步：今天 09:15</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-download text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">数据导出</p>
                        <p class="text-xs text-ios-gray">导出为 CSV 或 Excel</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-upload text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">数据导入</p>
                        <p class="text-xs text-ios-gray">从其他应用导入数据</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 flex items-center">
                    <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-trash text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-ios-red">清除所有数据</p>
                        <p class="text-xs text-ios-gray">不可恢复，请谨慎操作</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
            </div>
            
            <!-- 帮助与反馈 -->
            <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                <h4 class="px-4 py-3 text-sm font-semibold text-ios-gray border-b border-ios-gray-5">帮助与反馈</h4>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-question-circle text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">使用帮助</p>
                        <p class="text-xs text-ios-gray">功能介绍与常见问题</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-heart text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">意见反馈</p>
                        <p class="text-xs text-ios-gray">告诉我们您的建议</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 border-b border-ios-gray-5 flex items-center">
                    <div class="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-star text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">给我们评分</p>
                        <p class="text-xs text-ios-gray">在 App Store 评分</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
                <div class="settings-item px-4 py-3 flex items-center">
                    <div class="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-info-circle text-white text-sm"></i>
                    </div>
                    <div class="flex-1">
                        <p class="font-medium text-gray-800">关于应用</p>
                        <p class="text-xs text-ios-gray">版本 2.1.0</p>
                    </div>
                    <i class="fas fa-chevron-right text-ios-gray"></i>
                </div>
            </div>
            
            <!-- 底部间距 -->
            <div class="h-20"></div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="absolute bottom-0 left-0 right-0 h-20 bg-white border-t border-ios-gray-5 flex">
            <button class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                <i class="fas fa-home text-lg mb-1"></i>
                <span class="text-xs">首页</span>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                <i class="fas fa-list text-lg mb-1"></i>
                <span class="text-xs">账单</span>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                <i class="fas fa-chart-pie text-lg mb-1"></i>
                <span class="text-xs">统计</span>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-ios-blue">
                <i class="fas fa-cog text-lg mb-1"></i>
                <span class="text-xs font-medium">设置</span>
            </button>
        </div>
        
    </div>

    <script>
        // 切换开关功能
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.addEventListener('click', function() {
                this.classList.toggle('active');
            });
        });
        
        // 设置项点击效果
        document.querySelectorAll('.settings-item').forEach(item => {
            item.addEventListener('click', function() {
                // 这里可以添加跳转到具体设置页面的逻辑
                console.log('打开设置项:', this.querySelector('p').textContent);
            });
        });
    </script>
</body>
</html> 