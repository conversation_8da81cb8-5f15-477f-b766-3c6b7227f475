<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:strokeWidth="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 账户信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 账户信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <!-- 账户名称和类型 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:id="@+id/tv_account_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="现金账户" />

                    <TextView
                        android:id="@+id/tv_account_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_tag"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="4dp"
                        android:textSize="12sp"
                        android:textColor="?attr/colorPrimary"
                        tools:text="现金" />

                </LinearLayout>

                <!-- 余额信息 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="4dp">

                    <TextView
                        android:id="@+id/tv_current_balance"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        tools:text="¥1,000.00" />

                    <TextView
                        android:id="@+id/tv_initial_balance"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurfaceVariant"
                        tools:text="初始: ¥1,000.00" />

                </LinearLayout>

            </LinearLayout>

            <!-- 状态开关 -->
            <Switch
                android:id="@+id/switch_active"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp" />

        </LinearLayout>

        <!-- 操作按钮行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="12dp">

            <ImageButton
                android:id="@+id/btn_edit"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_edit"
                android:contentDescription="@string/edit"
                android:layout_marginEnd="8dp"
                app:tint="?attr/colorPrimary" />

            <ImageButton
                android:id="@+id/btn_delete"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_delete"
                android:contentDescription="@string/delete"
                app:tint="?attr/colorError" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
