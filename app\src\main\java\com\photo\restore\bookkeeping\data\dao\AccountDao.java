package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.enums.AccountType;

import java.math.BigDecimal;
import java.util.List;

/**
 * 账户数据访问对象
 */
@Dao
public interface AccountDao {

    /**
     * 获取所有活跃账户（LiveData）
     * @return 账户列表LiveData
     */
    @Query("SELECT * FROM accounts WHERE is_active = 1 ORDER BY created_at ASC")
    LiveData<List<Account>> getAllActiveAccountsLiveData();

    /**
     * 获取所有活跃账户（同步）
     * @return 账户列表
     */
    @Query("SELECT * FROM accounts WHERE is_active = 1 ORDER BY created_at ASC")
    List<Account> getAllActiveAccounts();

    /**
     * 获取所有账户（包括非活跃）（LiveData）
     * @return 账户列表LiveData
     */
    @Query("SELECT * FROM accounts ORDER BY created_at ASC")
    LiveData<List<Account>> getAllAccountsLiveData();

    /**
     * 根据ID获取账户
     * @param accountId 账户ID
     * @return 账户
     */
    @Query("SELECT * FROM accounts WHERE id = :accountId")
    Account getAccountById(String accountId);

    /**
     * 根据ID获取账户（LiveData）
     * @param accountId 账户ID
     * @return 账户LiveData
     */
    @Query("SELECT * FROM accounts WHERE id = :accountId")
    LiveData<Account> getAccountByIdLiveData(String accountId);

    /**
     * 根据类型获取账户
     * @param type 账户类型
     * @return 账户列表
     */
    @Query("SELECT * FROM accounts WHERE type = :type AND is_active = 1 ORDER BY created_at ASC")
    List<Account> getAccountsByType(AccountType type);

    /**
     * 根据货币获取账户
     * @param currency 货币代码
     * @return 账户列表
     */
    @Query("SELECT * FROM accounts WHERE currency = :currency AND is_active = 1 ORDER BY created_at ASC")
    List<Account> getAccountsByCurrency(String currency);

    /**
     * 获取总资产
     * @return 总资产
     */
    @Query("SELECT COALESCE(SUM(current_balance), 0) FROM accounts WHERE is_active = 1")
    BigDecimal getTotalAssets();

    /**
     * 获取总资产（LiveData）
     * @return 总资产LiveData
     */
    @Query("SELECT COALESCE(SUM(current_balance), 0) FROM accounts WHERE is_active = 1")
    LiveData<BigDecimal> getTotalAssetsLiveData();

    /**
     * 根据货币获取总资产
     * @param currency 货币代码
     * @return 总资产
     */
    @Query("SELECT SUM(current_balance) FROM accounts WHERE currency = :currency AND is_active = 1")
    BigDecimal getTotalAssetsByCurrency(String currency);

    /**
     * 插入账户
     * @param account 账户
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertAccount(Account account);

    /**
     * 批量插入账户
     * @param accounts 账户列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertAccounts(List<Account> accounts);

    /**
     * 更新账户
     * @param account 账户
     */
    @Update
    void updateAccount(Account account);

    /**
     * 更新账户余额
     * @param accountId 账户ID
     * @param balance 新余额
     * @param updatedAt 更新时间
     */
    @Query("UPDATE accounts SET current_balance = :balance, updated_at = :updatedAt WHERE id = :accountId")
    void updateAccountBalance(String accountId, BigDecimal balance, long updatedAt);

    /**
     * 增加账户余额
     * @param accountId 账户ID
     * @param amount 增加金额
     * @param updatedAt 更新时间
     */
    @Query("UPDATE accounts SET current_balance = current_balance + :amount, updated_at = :updatedAt WHERE id = :accountId")
    void increaseAccountBalance(String accountId, BigDecimal amount, long updatedAt);

    /**
     * 减少账户余额
     * @param accountId 账户ID
     * @param amount 减少金额
     * @param updatedAt 更新时间
     */
    @Query("UPDATE accounts SET current_balance = current_balance - :amount, updated_at = :updatedAt WHERE id = :accountId")
    void decreaseAccountBalance(String accountId, BigDecimal amount, long updatedAt);

    /**
     * 软删除账户（设置为非活跃）
     * @param accountId 账户ID
     * @param updatedAt 更新时间
     */
    @Query("UPDATE accounts SET is_active = 0, updated_at = :updatedAt WHERE id = :accountId")
    void softDeleteAccount(String accountId, long updatedAt);

    /**
     * 删除账户
     * @param account 账户
     */
    @Delete
    void deleteAccount(Account account);

    /**
     * 获取账户数量
     * @return 账户数量
     */
    @Query("SELECT COUNT(*) FROM accounts WHERE is_active = 1")
    int getAccountCount();

    /**
     * 根据名称查找账户
     * @param name 账户名称
     * @return 账户对象
     */
    @Query("SELECT * FROM accounts WHERE name = :name AND is_active = 1 LIMIT 1")
    Account getAccountByName(String name);
}
