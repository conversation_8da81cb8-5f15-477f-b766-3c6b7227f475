package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 汇率实体类
 */
@Entity(
    tableName = "exchange_rates",
    indices = {
        @Index(value = {"from_currency", "to_currency"}, unique = true),
        @Index("last_updated")
    }
)
public class ExchangeRate {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "from_currency")
    private String fromCurrency;
    
    @ColumnInfo(name = "to_currency")
    private String toCurrency;
    
    @ColumnInfo(name = "rate")
    private BigDecimal rate;
    
    @ColumnInfo(name = "last_updated")
    private Date lastUpdated;

    // 构造函数
    public ExchangeRate() {
        this.id = UUID.randomUUID().toString();
        this.rate = BigDecimal.ONE;
        this.lastUpdated = new Date();
    }

    public ExchangeRate(String fromCurrency, String toCurrency, BigDecimal rate) {
        this();
        this.fromCurrency = fromCurrency;
        this.toCurrency = toCurrency;
        this.rate = rate;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFromCurrency() {
        return fromCurrency;
    }

    public void setFromCurrency(String fromCurrency) {
        this.fromCurrency = fromCurrency;
    }

    public String getToCurrency() {
        return toCurrency;
    }

    public void setToCurrency(String toCurrency) {
        this.toCurrency = toCurrency;
    }

    public BigDecimal getRate() {
        return rate;
    }

    public void setRate(BigDecimal rate) {
        this.rate = rate;
    }

    public Date getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(Date lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    /**
     * 转换金额
     * @param amount 原始金额
     * @return 转换后的金额
     */
    public BigDecimal convertAmount(BigDecimal amount) {
        if (amount == null || rate == null) {
            return BigDecimal.ZERO;
        }
        return amount.multiply(rate);
    }

    /**
     * 检查汇率是否过期（超过24小时）
     * @return true如果过期
     */
    public boolean isExpired() {
        if (lastUpdated == null) {
            return true;
        }
        long diffInMillis = new Date().getTime() - lastUpdated.getTime();
        long diffInHours = diffInMillis / (1000 * 60 * 60);
        return diffInHours > 24;
    }

    /**
     * 获取汇率对的标识符
     * @return 汇率对标识符
     */
    public String getCurrencyPair() {
        return fromCurrency + "/" + toCurrency;
    }
}
