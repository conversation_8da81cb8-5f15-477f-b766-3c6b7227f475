package com.photo.restore.bookkeeping.data.entity;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.photo.restore.bookkeeping.data.enums.AccountType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 账户实体类
 */
@Entity(tableName = "accounts")
public class Account {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "type")
    private AccountType type;
    
    @ColumnInfo(name = "currency")
    private String currency;
    
    @ColumnInfo(name = "initial_balance")
    private BigDecimal initialBalance;
    
    @ColumnInfo(name = "current_balance")
    private BigDecimal currentBalance;
    
    @ColumnInfo(name = "is_active")
    private boolean isActive;
    
    @ColumnInfo(name = "icon_name")
    private String iconName;
    
    @ColumnInfo(name = "color")
    private String color;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // 构造函数
    public Account() {
        this.id = UUID.randomUUID().toString();
        this.isActive = true;
        this.initialBalance = BigDecimal.ZERO;
        this.currentBalance = BigDecimal.ZERO;
        this.currency = "CNY";
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    public Account(String name, AccountType type, String currency, BigDecimal initialBalance) {
        this();
        this.name = name;
        this.type = type;
        this.currency = currency;
        this.initialBalance = initialBalance;
        this.currentBalance = initialBalance;
        this.iconName = type.getIconClass();
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AccountType getType() {
        return type;
    }

    public void setType(AccountType type) {
        this.type = type;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getInitialBalance() {
        return initialBalance;
    }

    public void setInitialBalance(BigDecimal initialBalance) {
        this.initialBalance = initialBalance;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取账户类型的显示名称
     * @param context 上下文
     * @return 显示名称
     */
    public String getTypeDisplayName(Context context) {
        return type != null ? type.getDisplayName(context) : "";
    }

    /**
     * 获取账户类型的显示名称（保持向后兼容）
     * @param isEnglish 是否为英文
     * @return 显示名称
     * @deprecated 使用 getTypeDisplayName(Context context) 替代
     */
    @Deprecated
    public String getTypeDisplayName(boolean isEnglish) {
        return type != null ? type.getDisplayName(isEnglish) : "";
    }
}
