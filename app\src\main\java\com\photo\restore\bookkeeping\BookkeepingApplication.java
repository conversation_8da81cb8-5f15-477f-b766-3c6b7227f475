package com.photo.restore.bookkeeping;

import android.app.Application;
import android.content.Context;

import com.photo.restore.bookkeeping.utils.LocaleUtils;

/**
 * 应用程序Application类
 * 处理全局配置和语言设置
 */
public class BookkeepingApplication extends Application {

    @Override
    protected void attachBaseContext(Context base) {
        // 设置默认语言为英语
        Context context = LocaleUtils.setLocale(base, "en-US");
        super.attachBaseContext(context);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 其他全局初始化可以在这里进行
    }
}
