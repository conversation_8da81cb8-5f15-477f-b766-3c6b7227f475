<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.add.AddTransactionFragment">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:layout_alignParentTop="true"
        android:background="?attr/colorSurface"
        app:title="@string/nav_add"
        app:titleTextColor="?attr/colorOnSurface" />

    <ScrollView
        android:id="@+id/scroll_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_above="@id/layout_keypad"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 收支切换 -->
            <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/toggle_transaction_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:singleSelection="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_expense"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/expense" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_income"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/income" />

            </com.google.android.material.button.MaterialButtonToggleGroup>

            <!-- 金额输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/amount"
                app:startIconDrawable="@drawable/ic_money">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_amount"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:textSize="18sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 分类选择 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_category"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/category">

                <AutoCompleteTextView
                    android:id="@+id/et_category"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 账户选择 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_account"
                style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/account">

                <AutoCompleteTextView
                    android:id="@+id/et_account"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 描述输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/description">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 备注输入 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_note"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="@string/note">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_note"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:maxLines="3" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 保存按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/save" />

        </LinearLayout>

    </ScrollView>

    <!-- 数字键盘 -->
    <include
        android:id="@+id/layout_keypad"
        layout="@layout/layout_number_keypad"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:visibility="gone" />

</RelativeLayout>
