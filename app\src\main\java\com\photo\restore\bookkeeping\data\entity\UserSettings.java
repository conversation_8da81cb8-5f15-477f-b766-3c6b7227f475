package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import java.util.Date;

/**
 * 用户设置实体类
 */
@Entity(tableName = "user_settings")
public class UserSettings {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "preferred_language")
    private String preferredLanguage; // zh-CN, en-US
    
    @ColumnInfo(name = "default_currency")
    private String defaultCurrency;
    
    @ColumnInfo(name = "enable_biometric_auth")
    private boolean enableBiometricAuth;
    
    @ColumnInfo(name = "enable_notifications")
    private boolean enableNotifications;
    
    @ColumnInfo(name = "dark_mode_enabled")
    private boolean darkModeEnabled;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // 构造函数
    public UserSettings() {
        this.id = "default_user";
        this.preferredLanguage = "zh-CN";
        this.defaultCurrency = "CNY";
        this.enableBiometricAuth = false;
        this.enableNotifications = true;
        this.darkModeEnabled = false;
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    // Getter和Setter方法
    @NonNull
    public String getId() {
        return id;
    }

    public void setId(@NonNull String id) {
        this.id = id;
    }

    public String getPreferredLanguage() {
        return preferredLanguage;
    }

    public void setPreferredLanguage(String preferredLanguage) {
        this.preferredLanguage = preferredLanguage;
    }

    public String getDefaultCurrency() {
        return defaultCurrency;
    }

    public void setDefaultCurrency(String defaultCurrency) {
        this.defaultCurrency = defaultCurrency;
    }

    public boolean isEnableBiometricAuth() {
        return enableBiometricAuth;
    }

    public void setEnableBiometricAuth(boolean enableBiometricAuth) {
        this.enableBiometricAuth = enableBiometricAuth;
    }

    public boolean isEnableNotifications() {
        return enableNotifications;
    }

    public void setEnableNotifications(boolean enableNotifications) {
        this.enableNotifications = enableNotifications;
    }

    public boolean isDarkModeEnabled() {
        return darkModeEnabled;
    }

    public void setDarkModeEnabled(boolean darkModeEnabled) {
        this.darkModeEnabled = darkModeEnabled;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 检查是否为英文语言
     * @return true如果是英文
     */
    public boolean isEnglish() {
        return "en-US".equals(preferredLanguage);
    }
}
