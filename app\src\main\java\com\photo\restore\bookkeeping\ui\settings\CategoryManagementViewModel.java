package com.photo.restore.bookkeeping.ui.settings;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.data.repository.CategoryRepository;

import java.util.Date;
import java.util.List;

/**
 * 分类管理ViewModel
 */
public class CategoryManagementViewModel extends AndroidViewModel {

    private final CategoryRepository categoryRepository;

    public CategoryManagementViewModel(@NonNull Application application) {
        super(application);
        categoryRepository = new CategoryRepository(application);
    }

    /**
     * 根据类型获取分类（包括非活跃的，用于管理界面）
     */
    public LiveData<List<Category>> getCategoriesByType(TransactionType type) {
        return categoryRepository.getAllCategoriesByTypeLiveData(type);
    }

    /**
     * 获取所有分类
     */
    public LiveData<List<Category>> getAllCategories() {
        return categoryRepository.getAllCategoriesLiveData();
    }

    /**
     * 添加分类
     */
    public void addCategory(Category category) {
        categoryRepository.insertCategory(category);
    }

    /**
     * 更新分类
     */
    public void updateCategory(Category category) {
        categoryRepository.updateCategory(category);
    }

    /**
     * 删除分类
     */
    public void deleteCategory(Category category) {
        categoryRepository.deleteCategory(category);
    }

    /**
     * 切换分类状态
     */
    public void toggleCategoryStatus(Category category) {
        // 创建一个新的Category对象来避免直接修改原对象
        Category updatedCategory = new Category();
        updatedCategory.setId(category.getId());
        updatedCategory.setName(category.getName());
        updatedCategory.setType(category.getType());
        updatedCategory.setIconName(category.getIconName());
        updatedCategory.setColor(category.getColor());
        updatedCategory.setParentCategoryId(category.getParentCategoryId());
        updatedCategory.setSystemDefault(category.isSystemDefault());
        updatedCategory.setCreatedAt(category.getCreatedAt());

        // 切换状态
        updatedCategory.setActive(!category.isActive());

        // 更新到数据库
        categoryRepository.updateCategory(updatedCategory);
    }
}
