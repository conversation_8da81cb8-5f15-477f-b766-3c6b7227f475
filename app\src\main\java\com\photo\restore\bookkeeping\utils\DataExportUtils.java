package com.photo.restore.bookkeeping.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Environment;
import android.widget.Toast;

import androidx.core.content.FileProvider;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 数据导出工具类
 */
public class DataExportUtils {

    private static final String CSV_HEADER = "日期,类型,分类,账户,金额,描述,备注\n";
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());

    /**
     * 导出交易数据为CSV文件
     */
    public static void exportTransactionsToCSV(Context context, List<TransactionWithDetails> transactions) {
        try {
            // 创建文件名
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = "money_transactions_" + timestamp + ".csv";
            
            // 创建文件
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File csvFile = new File(downloadsDir, fileName);
            
            // 写入CSV数据
            FileWriter writer = new FileWriter(csvFile);
            writer.write(CSV_HEADER);
            
            for (TransactionWithDetails transactionWithDetails : transactions) {
                Transaction transaction = transactionWithDetails.transaction;
                Category category = transactionWithDetails.category;
                Account account = transactionWithDetails.account;
                
                StringBuilder line = new StringBuilder();
                line.append(DATE_FORMAT.format(transaction.getDate())).append(",");
                line.append(transaction.getType().getDisplayName(false)).append(",");
                line.append(category != null ? category.getName() : "").append(",");
                line.append(account != null ? account.getName() : "").append(",");
                line.append(transaction.getAmount().toString()).append(",");
                line.append("\"").append(transaction.getDescription() != null ? transaction.getDescription() : "").append("\"").append(",");
                line.append("\"").append(transaction.getNote() != null ? transaction.getNote() : "").append("\"");
                line.append("\n");
                
                writer.write(line.toString());
            }
            
            writer.close();
            
            // 分享文件
            shareFile(context, csvFile);
            Toast.makeText(context, context.getString(R.string.export_success, fileName), Toast.LENGTH_LONG).show();
            
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(context, context.getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 导出账户数据为CSV文件
     */
    public static void exportAccountsToCSV(Context context, List<Account> accounts) {
        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = "money_accounts_" + timestamp + ".csv";
            
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File csvFile = new File(downloadsDir, fileName);
            
            FileWriter writer = new FileWriter(csvFile);
            writer.write("账户名称,账户类型,货币,初始余额,当前余额,创建时间\n");
            
            for (Account account : accounts) {
                StringBuilder line = new StringBuilder();
                line.append("\"").append(account.getName()).append("\"").append(",");
                line.append(account.getType().getDisplayName(false)).append(",");
                line.append(account.getCurrency()).append(",");
                line.append(account.getInitialBalance().toString()).append(",");
                line.append(account.getCurrentBalance().toString()).append(",");
                line.append(DATE_FORMAT.format(account.getCreatedAt()));
                line.append("\n");
                
                writer.write(line.toString());
            }
            
            writer.close();
            shareFile(context, csvFile);
            Toast.makeText(context, context.getString(R.string.export_success, fileName), Toast.LENGTH_LONG).show();
            
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(context, context.getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 导出分类数据为CSV文件
     */
    public static void exportCategoriesToCSV(Context context, List<Category> categories) {
        try {
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String fileName = "money_categories_" + timestamp + ".csv";
            
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File csvFile = new File(downloadsDir, fileName);
            
            FileWriter writer = new FileWriter(csvFile);
            writer.write("分类名称,类型,图标,颜色,父分类,是否系统默认,是否激活,创建时间\n");
            
            for (Category category : categories) {
                StringBuilder line = new StringBuilder();
                line.append("\"").append(category.getName()).append("\"").append(",");
                line.append(category.getType().getDisplayName(false)).append(",");
                line.append(category.getIconName() != null ? category.getIconName() : "").append(",");
                line.append(category.getColor() != null ? category.getColor() : "").append(",");
                line.append(category.getParentCategoryId() != null ? category.getParentCategoryId() : "").append(",");
                line.append(category.isSystemDefault() ? "是" : "否").append(",");
                line.append(category.isActive() ? "是" : "否").append(",");
                line.append(DATE_FORMAT.format(category.getCreatedAt()));
                line.append("\n");
                
                writer.write(line.toString());
            }
            
            writer.close();
            shareFile(context, csvFile);
            Toast.makeText(context, context.getString(R.string.export_success, fileName), Toast.LENGTH_LONG).show();
            
        } catch (IOException e) {
            e.printStackTrace();
            Toast.makeText(context, context.getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 分享文件
     */
    private static void shareFile(Context context, File file) {
        try {
            Uri fileUri = FileProvider.getUriForFile(context, 
                context.getPackageName() + ".fileprovider", file);
            
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/csv");
            shareIntent.putExtra(Intent.EXTRA_STREAM, fileUri);
            shareIntent.putExtra(Intent.EXTRA_SUBJECT, "Money记账数据导出");
            shareIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            
            context.startActivity(Intent.createChooser(shareIntent, "分享CSV文件"));
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(context, context.getString(R.string.share_failed, e.getMessage()), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 创建备份文件
     */
    public static File createBackupFile(Context context) throws IOException {
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = "money_backup_" + timestamp + ".db";
        
        File backupDir = new File(context.getExternalFilesDir(null), "backups");
        if (!backupDir.exists()) {
            backupDir.mkdirs();
        }
        
        return new File(backupDir, fileName);
    }

    /**
     * 检查存储权限
     */
    public static boolean hasStoragePermission(Context context) {
        // Android 10+ 使用分区存储，不需要存储权限
        return android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q ||
               context.checkSelfPermission(android.Manifest.permission.WRITE_EXTERNAL_STORAGE) 
               == android.content.pm.PackageManager.PERMISSION_GRANTED;
    }
}
