package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 交易记录适配器
 */
public class TransactionAdapter extends RecyclerView.Adapter<TransactionAdapter.TransactionViewHolder> {

    private List<Transaction> transactions;
    private final OnTransactionClickListener clickListener;
    private final NumberFormat currencyFormat;
    private final SimpleDateFormat timeFormat;

    /**
     * 交易点击监听器接口
     */
    public interface OnTransactionClickListener {
        void onTransactionClick(Transaction transaction);
    }

    public TransactionAdapter(List<Transaction> transactions, OnTransactionClickListener clickListener) {
        this.transactions = transactions;
        this.clickListener = clickListener;
        
        // 初始化格式化器
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        this.currencyFormat.setCurrency(Currency.getInstance("CNY"));
        this.timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public TransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_transaction, parent, false);
        return new TransactionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TransactionViewHolder holder, int position) {
        Transaction transaction = transactions.get(position);
        holder.bind(transaction);
    }

    @Override
    public int getItemCount() {
        return transactions != null ? transactions.size() : 0;
    }

    /**
     * 更新交易列表
     */
    public void updateTransactions(List<Transaction> newTransactions) {
        this.transactions = newTransactions;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    class TransactionViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView tvCategoryIcon;
        private final TextView tvDescription;
        private final TextView tvCategory;
        private final TextView tvAccount;
        private final TextView tvAmount;
        private final TextView tvTime;

        public TransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvCategoryIcon = itemView.findViewById(R.id.tv_category_icon);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvCategory = itemView.findViewById(R.id.tv_category);
            tvAccount = itemView.findViewById(R.id.tv_account);
            tvAmount = itemView.findViewById(R.id.tv_amount);
            tvTime = itemView.findViewById(R.id.tv_time);
            
            // 设置点击监听器
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null) {
                    clickListener.onTransactionClick(transactions.get(position));
                }
            });
        }

        public void bind(Transaction transaction) {
            Context context = itemView.getContext();
            
            // 设置描述
            tvDescription.setText(transaction.getDescription() != null && !transaction.getDescription().isEmpty() 
                ? transaction.getDescription() 
                : getDefaultDescription(transaction.getType()));
            
            // 设置分类图标和名称
            // TODO: 从分类实体获取图标和名称，这里先使用默认值
            tvCategoryIcon.setText(getDefaultCategoryIcon(transaction.getType()));
            tvCategory.setText(context.getString(R.string.default_category)); // TODO: 从分类实体获取名称

            // 设置账户名称
            // TODO: 从账户实体获取名称，这里先使用默认值
            tvAccount.setText(context.getString(R.string.default_account)); // TODO: 从账户实体获取名称
            
            // 设置金额
            BigDecimal amount = transaction.getAmount();
            String amountText;
            int amountColor;
            
            if (transaction.getType() == TransactionType.INCOME) {
                amountText = "+" + currencyFormat.format(amount);
                amountColor = ContextCompat.getColor(context, R.color.income_color);
            } else {
                amountText = "-" + currencyFormat.format(amount);
                amountColor = ContextCompat.getColor(context, R.color.expense_color);
            }
            
            tvAmount.setText(amountText);
            tvAmount.setTextColor(amountColor);
            
            // 设置时间
            if (transaction.getDate() != null) {
                tvTime.setText(timeFormat.format(transaction.getDate()));
            } else {
                tvTime.setText("");
            }
        }

        /**
         * 获取默认描述
         */
        private String getDefaultDescription(TransactionType type) {
            Context context = itemView.getContext();
            if (type == TransactionType.INCOME) {
                return context.getString(R.string.income);
            } else {
                return context.getString(R.string.expense);
            }
        }

        /**
         * 获取默认分类图标
         */
        private String getDefaultCategoryIcon(TransactionType type) {
            if (type == TransactionType.INCOME) {
                return "💰"; // 收入默认图标
            } else {
                return "🛒"; // 支出默认图标
            }
        }
    }
}
