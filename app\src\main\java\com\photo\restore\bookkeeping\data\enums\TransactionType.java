package com.photo.restore.bookkeeping.data.enums;

import android.content.Context;
import com.photo.restore.bookkeeping.R;

/**
 * 交易类型枚举
 */
public enum TransactionType {
    /**
     * 收入
     */
    INCOME(R.string.transaction_type_income),

    /**
     * 支出
     */
    EXPENSE(R.string.transaction_type_expense),

    /**
     * 转账
     */
    TRANSFER(R.string.transaction_type_transfer);

    private final int stringResourceId;

    TransactionType(int stringResourceId) {
        this.stringResourceId = stringResourceId;
    }

    /**
     * 获取显示名称
     * @param context 上下文
     * @return 显示名称
     */
    public String getDisplayName(Context context) {
        return context.getString(stringResourceId);
    }

    /**
     * 根据语言获取显示名称（保持向后兼容）
     * @param isEnglish 是否为英文
     * @return 显示名称
     * @deprecated 使用 getDisplayName(Context context) 替代
     */
    @Deprecated
    public String getDisplayName(boolean isEnglish) {
        // 为了向后兼容，保留此方法，但建议使用新方法
        switch (this) {
            case INCOME:
                return isEnglish ? "Income" : "收入";
            case EXPENSE:
                return isEnglish ? "Expense" : "支出";
            case TRANSFER:
                return isEnglish ? "Transfer" : "转账";
            default:
                return isEnglish ? "Unknown" : "未知";
        }
    }
}
