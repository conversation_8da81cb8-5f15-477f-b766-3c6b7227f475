package com.photo.restore.bookkeeping.data.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.data.dao.UserSettingsDao;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;
import com.photo.restore.bookkeeping.data.entity.UserSettings;

import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 用户设置仓库类
 */
public class UserSettingsRepository {

    private final UserSettingsDao userSettingsDao;
    private final ExecutorService executor;
    private final LiveData<UserSettings> userSettingsLiveData;

    public UserSettingsRepository(Application application) {
        BookkeepingDatabase database = BookkeepingDatabase.getInstance(application);
        userSettingsDao = database.userSettingsDao();
        executor = Executors.newFixedThreadPool(2);
        userSettingsLiveData = userSettingsDao.getUserSettingsLiveData();
    }

    /**
     * 获取用户设置（LiveData）
     * @return 用户设置LiveData
     */
    public LiveData<UserSettings> getUserSettingsLiveData() {
        return userSettingsLiveData;
    }

    /**
     * 获取用户设置（同步）
     * @return 用户设置
     */
    public UserSettings getUserSettings() {
        return userSettingsDao.getUserSettings();
    }

    /**
     * 初始化默认用户设置
     */
    public void initializeDefaultSettings() {
        executor.execute(() -> {
            if (userSettingsDao.getUserSettingsCount() == 0) {
                UserSettings defaultSettings = new UserSettings();
                defaultSettings.setId("default_user");
                userSettingsDao.insertUserSettings(defaultSettings);
            }
        });
    }

    /**
     * 更新用户设置
     * @param userSettings 用户设置
     */
    public void updateUserSettings(UserSettings userSettings) {
        executor.execute(() -> {
            userSettings.setUpdatedAt(new Date());
            userSettingsDao.updateUserSettings(userSettings);
        });
    }

    /**
     * 更新首选语言
     * @param language 语言代码
     */
    public void updatePreferredLanguage(String language) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            userSettingsDao.updatePreferredLanguage(language, currentTime);
        });
    }

    /**
     * 更新默认货币
     * @param currency 货币代码
     */
    public void updateDefaultCurrency(String currency) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            userSettingsDao.updateDefaultCurrency(currency, currentTime);
        });
    }

    /**
     * 更新深色模式设置
     * @param enabled 是否启用深色模式
     */
    public void updateDarkMode(boolean enabled) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            userSettingsDao.updateDarkMode(enabled, currentTime);
        });
    }

    /**
     * 更新生物识别认证设置
     * @param enabled 是否启用生物识别认证
     */
    public void updateBiometricAuth(boolean enabled) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            userSettingsDao.updateBiometricAuth(enabled, currentTime);
        });
    }

    /**
     * 更新通知设置
     * @param enabled 是否启用通知
     */
    public void updateNotifications(boolean enabled) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            userSettingsDao.updateNotifications(enabled, currentTime);
        });
    }

    /**
     * 获取首选语言
     * @return 语言代码
     */
    public String getPreferredLanguage() {
        UserSettings settings = getUserSettings();
        return settings != null ? settings.getPreferredLanguage() : "zh-CN";
    }

    /**
     * 获取默认货币
     * @return 货币代码
     */
    public String getDefaultCurrency() {
        UserSettings settings = getUserSettings();
        return settings != null ? settings.getDefaultCurrency() : "CNY";
    }

    /**
     * 检查是否启用深色模式
     * @return true如果启用深色模式
     */
    public boolean isDarkModeEnabled() {
        UserSettings settings = getUserSettings();
        return settings != null && settings.isDarkModeEnabled();
    }

    /**
     * 检查是否启用生物识别认证
     * @return true如果启用生物识别认证
     */
    public boolean isBiometricAuthEnabled() {
        UserSettings settings = getUserSettings();
        return settings != null && settings.isEnableBiometricAuth();
    }

    /**
     * 检查是否启用通知
     * @return true如果启用通知
     */
    public boolean isNotificationsEnabled() {
        UserSettings settings = getUserSettings();
        return settings != null && settings.isEnableNotifications();
    }
}
