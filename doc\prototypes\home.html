<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 主页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5B4BCF',
                        'secondary': '#A29BFE',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E84393',
                        'surface': '#FFFFFF',
                        'surface-alt': '#F8F9FA',
                        'neutral-50': '#FAFBFC',
                        'neutral-100': '#F4F5F7',
                        'neutral-200': '#E3E5E8',
                        'neutral-300': '#CFD3D8',
                        'neutral-400': '#A6ACB5',
                        'neutral-500': '#7B8794',
                        'neutral-600': '#5A6575',
                        'neutral-700': '#3E4651',
                        'neutral-800': '#2D3843',
                        'neutral-900': '#1F2937'
                    },
                    fontFamily: {
                        'display': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'system-ui', 'sans-serif'],
                        'body': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'system-ui', 'sans-serif']
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem'
                    },
                    borderRadius: {
                        '2xl': '1rem',
                        '3xl': '1.5rem',
                        '4xl': '2rem'
                    },
                    boxShadow: {
                        'soft': '0 2px 12px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 24px rgba(0, 0, 0, 0.06)',
                        'large': '0 8px 40px rgba(0, 0, 0, 0.08)',
                        'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 16px rgba(0, 0, 0, 0.04)',
                        'button': '0 2px 8px rgba(108, 92, 231, 0.2)',
                        'floating': '0 8px 32px rgba(108, 92, 231, 0.15)'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(145deg, #F8F9FA 0%, #E9ECEF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        .glass-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .balance-card {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 50%, #FD79A8 100%);
            position: relative;
            overflow: hidden;
        }
        
        .balance-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .action-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #FFFFFF 0%, #F8F9FA 100%);
        }
        
        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .action-button:active {
            transform: translateY(0);
            transition: all 0.1s;
        }
        
        .icon-gradient {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        .transaction-item {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border-left: 3px solid transparent;
        }
        
        .transaction-item:hover {
            background: linear-gradient(90deg, rgba(108, 92, 231, 0.02) 0%, rgba(255, 255, 255, 0.8) 100%);
            border-left-color: #6C5CE7;
            transform: translateX(2px);
        }
        
        .stat-card {
            background: linear-gradient(145deg, #FFFFFF 0%, #FAFBFC 100%);
            border: 1px solid rgba(108, 92, 231, 0.1);
        }
        
        .pulse-ring {
            animation: pulse-ring 2s infinite;
        }
        
        @keyframes pulse-ring {
            0% {
                transform: scale(0.8);
                opacity: 1;
            }
            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }
        
        .floating-action {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            box-shadow: 0 8px 32px rgba(108, 92, 231, 0.3), 0 0 0 0 rgba(108, 92, 231, 0.3);
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-4px); }
        }
        
        .nav-item {
            transition: all 0.2s ease;
        }
        
        .nav-item.active {
            color: #6C5CE7;
        }
        
        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: #6C5CE7;
            border-radius: 50%;
        }
        
        .amount-counter {
            font-feature-settings: 'tnum';
            letter-spacing: -0.02em;
        }
        
        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-neutral-50 to-neutral-100 min-h-screen flex items-center justify-center font-body">
    <div class="phone-container">
        
        <!-- 状态栏 -->
        <div class="h-11 bg-transparent flex items-center justify-between px-6 text-neutral-800 text-sm font-medium">
            <span class="font-semibold">9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <div class="flex items-center">
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                    <span class="text-xs ml-1">87</span>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="h-16 glass-card flex items-center justify-between px-6 border-b-0">
            <div class="flex items-center">
                <div class="w-10 h-10 icon-gradient from-primary to-secondary rounded-2xl mr-4 shadow-button">
                    <i class="fas fa-user text-white"></i>
                </div>
                <div>
                    <p class="text-base font-semibold text-neutral-800">早上好 👋</p>
                    <p class="text-sm text-neutral-500">李先生</p>
                </div>
            </div>
            <div class="flex items-center space-x-3">
                <button class="p-3 rounded-full bg-neutral-100 hover:bg-neutral-200 transition-all duration-200">
                    <i class="fas fa-search text-neutral-600"></i>
                </button>
                <button class="p-3 rounded-full bg-neutral-100 hover:bg-neutral-200 transition-all duration-200 relative">
                    <i class="fas fa-bell text-neutral-600"></i>
                    <div class="absolute -top-1 -right-1 w-3 h-3 bg-danger rounded-full"></div>
                </button>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="flex-1 overflow-y-auto px-6 py-6">
            
            <!-- 总余额卡片 -->
            <div class="balance-card rounded-3xl p-8 text-white mb-8 relative overflow-hidden shadow-large">
                <!-- 装饰元素 -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-5 rounded-full -mr-16 -mt-16"></div>
                <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full -ml-12 -mb-12"></div>
                <div class="absolute top-1/2 right-8 w-16 h-16 border border-white border-opacity-10 rounded-full"></div>
                
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-6">
                        <div>
                            <p class="text-white text-opacity-80 text-sm font-medium">总资产</p>
                            <div class="flex items-center mt-1">
                                <div class="w-2 h-2 bg-success rounded-full mr-2"></div>
                                <span class="text-xs text-white text-opacity-60">较上月 +12.5%</span>
                            </div>
                        </div>
                        <button class="p-2 rounded-xl bg-white bg-opacity-10 hover:bg-opacity-20 transition-all">
                            <i class="fas fa-eye text-white text-sm"></i>
                        </button>
                    </div>
                    
                    <h2 class="text-5xl font-bold mb-8 amount-counter">¥12,580.50</h2>
                    
                    <div class="grid grid-cols-2 gap-6">
                        <div class="text-center">
                            <div class="flex items-center justify-center mb-2">
                                <div class="w-3 h-3 bg-success rounded-full mr-2"></div>
                                <p class="text-white text-opacity-80 text-sm">本月收入</p>
                            </div>
                            <p class="text-2xl font-bold">+¥8,500.00</p>
                        </div>
                        <div class="text-center">
                            <div class="flex items-center justify-center mb-2">
                                <div class="w-3 h-3 bg-accent rounded-full mr-2"></div>
                                <p class="text-white text-opacity-80 text-sm">本月支出</p>
                            </div>
                            <p class="text-2xl font-bold">-¥3,240.00</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快速操作 -->
            <div class="grid grid-cols-4 gap-4 mb-8">
                <button class="action-button card-hover rounded-2xl p-4 text-center shadow-card">
                    <div class="w-14 h-14 icon-gradient from-success to-emerald-400 rounded-2xl mx-auto mb-3 shadow-button">
                        <i class="fas fa-plus text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">记收入</p>
                </button>
                <button class="action-button card-hover rounded-2xl p-4 text-center shadow-card">
                    <div class="w-14 h-14 icon-gradient from-danger to-pink-400 rounded-2xl mx-auto mb-3 shadow-button">
                        <i class="fas fa-minus text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">记支出</p>
                </button>
                <button class="action-button card-hover rounded-2xl p-4 text-center shadow-card">
                    <div class="w-14 h-14 icon-gradient from-primary to-secondary rounded-2xl mx-auto mb-3 shadow-button">
                        <i class="fas fa-exchange-alt text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">转账</p>
                </button>
                <button class="action-button card-hover rounded-2xl p-4 text-center shadow-card">
                    <div class="w-14 h-14 icon-gradient from-warning to-yellow-400 rounded-2xl mx-auto mb-3 shadow-button">
                        <i class="fas fa-chart-line text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">分析</p>
                </button>
            </div>
            
            <!-- 账户概览 -->
            <div class="stat-card card-hover rounded-3xl p-6 mb-6 shadow-card">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-neutral-800">我的账户</h3>
                    <button class="text-primary text-sm font-semibold hover:text-primary-dark transition-colors">
                        查看全部 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-blue-50 to-blue-100 border border-blue-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 icon-gradient from-blue-500 to-blue-600 rounded-2xl mr-4 shadow-soft">
                                <i class="fas fa-credit-card text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-neutral-800">招商银行</p>
                                <p class="text-xs text-neutral-500">储蓄卡 ···· 8888</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 amount-counter">¥8,240.50</p>
                            <p class="text-xs text-success">+2.3%</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-green-50 to-green-100 border border-green-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 icon-gradient from-success to-emerald-500 rounded-2xl mr-4 shadow-soft">
                                <i class="fas fa-wallet text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-neutral-800">现金</p>
                                <p class="text-xs text-neutral-500">现金账户</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 amount-counter">¥340.00</p>
                            <p class="text-xs text-neutral-400">-</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between p-4 rounded-2xl bg-gradient-to-r from-indigo-50 to-indigo-100 border border-indigo-200">
                        <div class="flex items-center">
                            <div class="w-12 h-12 icon-gradient from-indigo-500 to-indigo-600 rounded-2xl mr-4 shadow-soft">
                                <i class="fab fa-alipay text-white"></i>
                            </div>
                            <div>
                                <p class="font-semibold text-neutral-800">支付宝</p>
                                <p class="text-xs text-neutral-500">电子钱包</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 amount-counter">¥4,000.00</p>
                            <p class="text-xs text-success">+5.1%</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 近期交易 -->
            <div class="stat-card card-hover rounded-3xl p-6 shadow-card">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-neutral-800">近期交易</h3>
                    <button class="text-primary text-sm font-semibold hover:text-primary-dark transition-colors">
                        查看全部 <i class="fas fa-arrow-right ml-1"></i>
                    </button>
                </div>
                <div class="space-y-1">
                    <div class="transaction-item flex items-center p-4 rounded-2xl">
                        <div class="w-12 h-12 icon-gradient from-warning to-orange-400 rounded-2xl mr-4 shadow-soft">
                            <i class="fas fa-utensils text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <p class="font-semibold text-neutral-800">午餐 - 沙县小吃</p>
                                <span class="text-danger font-bold amount-counter">-¥35.00</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-xs text-neutral-500">餐饮 · 现金</p>
                                <p class="text-xs text-neutral-400">今天 12:30</p>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item flex items-center p-4 rounded-2xl">
                        <div class="w-12 h-12 icon-gradient from-primary to-blue-500 rounded-2xl mr-4 shadow-soft">
                            <i class="fas fa-bus text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <p class="font-semibold text-neutral-800">地铁</p>
                                <span class="text-danger font-bold amount-counter">-¥4.00</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-xs text-neutral-500">交通 · 支付宝</p>
                                <p class="text-xs text-neutral-400">今天 08:30</p>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item flex items-center p-4 rounded-2xl">
                        <div class="w-12 h-12 icon-gradient from-success to-emerald-500 rounded-2xl mr-4 shadow-soft">
                            <i class="fas fa-hand-holding-usd text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center justify-between mb-1">
                                <p class="font-semibold text-neutral-800">工资</p>
                                <span class="text-success font-bold amount-counter">+¥8,500.00</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-xs text-neutral-500">薪资收入 · 银行卡</p>
                                <p class="text-xs text-neutral-400">昨天 09:00</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 底部间距 -->
            <div class="h-24"></div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="absolute bottom-0 left-0 right-0 h-20 glass-card border-t border-white border-opacity-20 flex">
            <button class="nav-item active flex-1 flex flex-col items-center justify-center relative">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="nav-item flex-1 flex flex-col items-center justify-center text-neutral-400 relative">
                <i class="fas fa-list text-xl mb-1"></i>
                <span class="text-xs">账单</span>
            </button>
            <button class="nav-item flex-1 flex flex-col items-center justify-center text-neutral-400 relative">
                <i class="fas fa-chart-pie text-xl mb-1"></i>
                <span class="text-xs">统计</span>
            </button>
            <button class="nav-item flex-1 flex flex-col items-center justify-center text-neutral-400 relative">
                <i class="fas fa-cog text-xl mb-1"></i>
                <span class="text-xs">设置</span>
            </button>
        </div>
        
        <!-- 浮动记账按钮 -->
        <button class="floating-action absolute bottom-24 right-6 w-16 h-16 rounded-2xl flex items-center justify-center">
            <div class="pulse-ring absolute inset-0 rounded-2xl bg-primary opacity-20"></div>
            <i class="fas fa-plus text-white text-xl relative z-10"></i>
        </button>
        
    </div>
</body>
</html> 
</html> 