<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/homeFragment">

    <fragment
        android:id="@+id/homeFragment"
        android:name="com.photo.restore.bookkeeping.ui.home.HomeFragment"
        android:label="@string/nav_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/transactionsFragment"
        android:name="com.photo.restore.bookkeeping.ui.transactions.TransactionsFragment"
        android:label="@string/nav_transactions"
        tools:layout="@layout/fragment_transactions" />

    <fragment
        android:id="@+id/addTransactionFragment"
        android:name="com.photo.restore.bookkeeping.ui.add.AddTransactionFragment"
        android:label="@string/nav_add"
        tools:layout="@layout/fragment_add_transaction" />

    <fragment
        android:id="@+id/statisticsFragment"
        android:name="com.photo.restore.bookkeeping.ui.statistics.StatisticsFragment"
        android:label="@string/nav_statistics"
        tools:layout="@layout/fragment_statistics" />

    <fragment
        android:id="@+id/settingsFragment"
        android:name="com.photo.restore.bookkeeping.ui.settings.SettingsFragment"
        android:label="@string/nav_settings"
        tools:layout="@layout/fragment_settings">

        <action
            android:id="@+id/action_settings_to_account_management"
            app:destination="@id/accountManagementFragment" />

        <action
            android:id="@+id/action_settings_to_category_management"
            app:destination="@id/categoryManagementFragment" />
    </fragment>

    <fragment
        android:id="@+id/accountManagementFragment"
        android:name="com.photo.restore.bookkeeping.ui.settings.AccountManagementFragment"
        android:label="@string/account_management"
        tools:layout="@layout/fragment_account_management" />

    <fragment
        android:id="@+id/categoryManagementFragment"
        android:name="com.photo.restore.bookkeeping.ui.settings.CategoryManagementFragment"
        android:label="@string/category_management"
        tools:layout="@layout/fragment_category_management" />

</navigation>
