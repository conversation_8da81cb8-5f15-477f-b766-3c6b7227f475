package com.photo.restore.bookkeeping.ui.statistics;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.button.MaterialButtonToggleGroup;
import android.util.Log;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.ui.adapter.CategoryStatisticAdapter;
import com.photo.restore.bookkeeping.ui.view.ChartLegendView;
import com.photo.restore.bookkeeping.ui.view.PieChartView;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.dao.TransactionDao;
import com.photo.restore.bookkeeping.data.repository.CategoryRepository;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 统计分析Fragment
 * 显示收支统计和分类分析
 */
public class StatisticsFragment extends Fragment {

    private StatisticsViewModel statisticsViewModel;
    private CategoryRepository categoryRepository;
    private ExecutorService executor;

    // UI组件
    private MaterialToolbar toolbar;
    private MaterialButtonToggleGroup togglePeriod;
    private MaterialButton btnMonth;
    private MaterialButton btnYear;
    private TextView tvTotalIncome;
    private TextView tvTotalExpense;
    private TextView tvNetIncome;
    private RecyclerView rvCategoryStatistics;
    private PieChartView pieChart;
    private ChartLegendView chartLegend;
    private View chartContainer;
    private View emptyStateContainer;

    // 适配器
    private CategoryStatisticAdapter categoryStatisticAdapter;

    // 格式化器
    private NumberFormat currencyFormat;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化ViewModel和Repository
        statisticsViewModel = new ViewModelProvider(this).get(StatisticsViewModel.class);
        categoryRepository = new CategoryRepository(requireActivity().getApplication());
        executor = Executors.newSingleThreadExecutor();

        // 初始化货币格式化器
        currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        currencyFormat.setCurrency(Currency.getInstance("CNY"));
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_statistics, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupRecyclerView();
        setupClickListeners();
        observeData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        togglePeriod = view.findViewById(R.id.toggle_period);
        btnMonth = view.findViewById(R.id.btn_month);
        btnYear = view.findViewById(R.id.btn_year);
        tvTotalIncome = view.findViewById(R.id.tv_total_income);
        tvTotalExpense = view.findViewById(R.id.tv_total_expense);
        tvNetIncome = view.findViewById(R.id.tv_net_income);
        rvCategoryStatistics = view.findViewById(R.id.rv_category_statistics);
        pieChart = view.findViewById(R.id.pie_chart);
        chartLegend = view.findViewById(R.id.chart_legend);
        chartContainer = view.findViewById(R.id.chart_container);
        emptyStateContainer = view.findViewById(R.id.empty_state_container);
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        categoryStatisticAdapter = new CategoryStatisticAdapter(new ArrayList<>(), this::onCategoryClick);
        rvCategoryStatistics.setLayoutManager(new LinearLayoutManager(getContext()));
        rvCategoryStatistics.setAdapter(categoryStatisticAdapter);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 时间周期切换
        togglePeriod.addOnButtonCheckedListener((group, checkedId, isChecked) -> {
            if (isChecked) {
                if (checkedId == R.id.btn_month) {
                    statisticsViewModel.setPeriod(StatisticsViewModel.Period.MONTH);
                } else if (checkedId == R.id.btn_year) {
                    statisticsViewModel.setPeriod(StatisticsViewModel.Period.YEAR);
                }
            }
        });
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        // 观察总收入
        statisticsViewModel.getTotalIncome().observe(getViewLifecycleOwner(), income -> {
            if (income != null) {
                tvTotalIncome.setText(formatCurrency(income));
            } else {
                tvTotalIncome.setText(formatCurrency(BigDecimal.ZERO));
            }
        });

        // 观察总支出
        statisticsViewModel.getTotalExpense().observe(getViewLifecycleOwner(), expense -> {
            if (expense != null) {
                tvTotalExpense.setText(formatCurrency(expense));
            } else {
                tvTotalExpense.setText(formatCurrency(BigDecimal.ZERO));
            }
        });

        // 观察净收入
        statisticsViewModel.getNetIncome().observe(getViewLifecycleOwner(), netIncome -> {
            if (netIncome != null) {
                tvNetIncome.setText(formatCurrency(netIncome));
                // 根据净收入正负设置颜色
                if (netIncome.compareTo(BigDecimal.ZERO) >= 0) {
                    tvNetIncome.setTextColor(ContextCompat.getColor(requireContext(), R.color.income_color));
                } else {
                    tvNetIncome.setTextColor(ContextCompat.getColor(requireContext(), R.color.expense_color));
                }
            } else {
                tvNetIncome.setText(formatCurrency(BigDecimal.ZERO));
                tvNetIncome.setTextColor(ContextCompat.getColor(requireContext(), R.color.income_color));
            }
        });

        // 观察分类统计
        statisticsViewModel.getCategoryStatistics().observe(getViewLifecycleOwner(), statistics -> {
            if (statistics != null) {
                convertAndUpdateCategoryStatistics(statistics);
            }
        });

        // 观察当前周期
        statisticsViewModel.getCurrentPeriod().observe(getViewLifecycleOwner(), period -> {
            if (period == StatisticsViewModel.Period.MONTH) {
                togglePeriod.check(R.id.btn_month);
            } else {
                togglePeriod.check(R.id.btn_year);
            }
        });
    }

    /**
     * 格式化货币显示
     */
    private String formatCurrency(BigDecimal amount) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        return currencyFormat.format(amount);
    }

    /**
     * 分类点击处理
     */
    private void onCategoryClick(CategoryStatisticAdapter.CategoryStatistic statistic) {
        // TODO: 处理分类点击，可以导航到该分类的详细交易列表
        // 例如：导航到交易列表页面，并筛选该分类的交易
    }

    /**
     * 转换并更新分类统计数据
     */
    private void convertAndUpdateCategoryStatistics(List<TransactionDao.CategoryExpenseStatistic> rawStatistics) {
        executor.execute(() -> {
            Log.d("StatisticsFragment", "Converting " + rawStatistics.size() + " raw statistics");

            List<CategoryStatisticAdapter.CategoryStatistic> categoryStatistics = new ArrayList<>();

            // 计算总金额用于百分比计算
            BigDecimal totalAmount = BigDecimal.ZERO;
            for (TransactionDao.CategoryExpenseStatistic stat : rawStatistics) {
                totalAmount = totalAmount.add(stat.totalAmount);
            }

            Log.d("StatisticsFragment", "Total amount: " + totalAmount);

            // 转换数据
            for (TransactionDao.CategoryExpenseStatistic stat : rawStatistics) {
                Log.d("StatisticsFragment", "Processing category ID: " + stat.categoryId + ", amount: " + stat.totalAmount);

                // 获取分类信息
                Category category = categoryRepository.getCategoryById(stat.categoryId);
                if (category != null) {
                    Log.d("StatisticsFragment", "Found category: " + category.getName() + " (system: " + category.isSystemDefault() + ")");

                    // 计算百分比
                    double percentage = 0.0;
                    if (totalAmount.compareTo(BigDecimal.ZERO) > 0) {
                        percentage = stat.totalAmount.divide(totalAmount, 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100)).doubleValue();
                    }

                    CategoryStatisticAdapter.CategoryStatistic categoryStatistic =
                        new CategoryStatisticAdapter.CategoryStatistic(category, stat.totalAmount, percentage);
                    categoryStatistics.add(categoryStatistic);
                } else {
                    Log.w("StatisticsFragment", "Category not found for ID: " + stat.categoryId);
                }
            }

            Log.d("StatisticsFragment", "Final category statistics count: " + categoryStatistics.size());

            // 准备饼图数据
            List<PieChartView.PieSliceData> pieData = new ArrayList<>();
            for (CategoryStatisticAdapter.CategoryStatistic stat : categoryStatistics) {
                pieData.add(new PieChartView.PieSliceData(
                    stat.category.getName(),
                    stat.amount,
                    stat.category.getColor()
                ));
            }

            // 在主线程更新UI
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    Log.d("StatisticsFragment", "Updating adapter with " + categoryStatistics.size() + " items");
                    for (int i = 0; i < categoryStatistics.size(); i++) {
                        CategoryStatisticAdapter.CategoryStatistic stat = categoryStatistics.get(i);
                        Log.d("StatisticsFragment", "Item " + i + ": " + stat.category.getName() + " - " + stat.amount + " (" + stat.percentage + "%)");
                    }

                    categoryStatisticAdapter.updateStatistics(categoryStatistics);

                    // 根据数据状态显示不同的UI
                    if (pieData.isEmpty()) {
                        // 显示无数据状态
                        chartContainer.setVisibility(View.GONE);
                        emptyStateContainer.setVisibility(View.VISIBLE);
                    } else {
                        // 显示饼图和图例
                        chartContainer.setVisibility(View.VISIBLE);
                        emptyStateContainer.setVisibility(View.GONE);
                        pieChart.setData(pieData);
                        chartLegend.setLegendData(pieData);
                    }
                });
            }
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        // 刷新数据
        statisticsViewModel.refreshData();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
