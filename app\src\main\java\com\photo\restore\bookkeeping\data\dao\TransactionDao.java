package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.math.BigDecimal;
import java.util.List;

/**
 * 交易记录数据访问对象
 */
@Dao
public interface TransactionDao {

    /**
     * 获取所有交易记录（LiveData）
     * @return 交易记录列表LiveData
     */
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC")
    LiveData<List<Transaction>> getAllTransactionsLiveData();

    /**
     * 获取所有交易记录（同步）
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC")
    List<Transaction> getAllTransactions();

    /**
     * 获取最近的交易记录
     * @param limit 限制数量
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC LIMIT :limit")
    List<Transaction> getRecentTransactions(int limit);

    /**
     * 获取最近的交易记录（LiveData）
     * @param limit 限制数量
     * @return 交易记录列表LiveData
     */
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC LIMIT :limit")
    LiveData<List<Transaction>> getRecentTransactionsLiveData(int limit);

    /**
     * 获取最近的交易记录及其详细信息（LiveData）
     * @param limit 限制数量
     * @return 交易记录详细信息列表LiveData
     */
    @androidx.room.Transaction
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC LIMIT :limit")
    LiveData<List<TransactionWithDetails>> getRecentTransactionsWithDetailsLiveData(int limit);

    /**
     * 根据类型获取交易记录
     * @param type 交易类型
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions WHERE type = :type ORDER BY date DESC, created_at DESC")
    List<Transaction> getTransactionsByType(TransactionType type);

    /**
     * 根据类型获取交易记录（LiveData）
     * @param type 交易类型
     * @return 交易记录列表LiveData
     */
    @Query("SELECT * FROM transactions WHERE type = :type ORDER BY date DESC, created_at DESC")
    LiveData<List<Transaction>> getTransactionsByTypeLiveData(TransactionType type);

    /**
     * 根据账户获取交易记录
     * @param accountId 账户ID
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions WHERE account_id = :accountId ORDER BY date DESC, created_at DESC")
    List<Transaction> getTransactionsByAccount(String accountId);

    /**
     * 根据账户获取交易记录（LiveData）
     * @param accountId 账户ID
     * @return 交易记录列表LiveData
     */
    @Query("SELECT * FROM transactions WHERE account_id = :accountId ORDER BY date DESC, created_at DESC")
    LiveData<List<Transaction>> getTransactionsByAccountLiveData(String accountId);

    /**
     * 根据分类获取交易记录
     * @param categoryId 分类ID
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions WHERE category_id = :categoryId ORDER BY date DESC, created_at DESC")
    List<Transaction> getTransactionsByCategory(String categoryId);

    /**
     * 根据日期范围获取交易记录
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, created_at DESC")
    List<Transaction> getTransactionsByDateRange(long startDate, long endDate);

    /**
     * 根据日期范围获取交易记录（LiveData）
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 交易记录列表LiveData
     */
    @Query("SELECT * FROM transactions WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, created_at DESC")
    LiveData<List<Transaction>> getTransactionsByDateRangeLiveData(long startDate, long endDate);

    /**
     * 根据ID获取交易记录
     * @param transactionId 交易ID
     * @return 交易记录
     */
    @Query("SELECT * FROM transactions WHERE id = :transactionId")
    Transaction getTransactionById(String transactionId);

    /**
     * 根据ID获取交易记录（LiveData）
     * @param transactionId 交易ID
     * @return 交易记录LiveData
     */
    @Query("SELECT * FROM transactions WHERE id = :transactionId")
    LiveData<Transaction> getTransactionByIdLiveData(String transactionId);

    /**
     * 获取收入总额
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 收入总额
     */
    @Query("SELECT SUM(amount) FROM transactions WHERE type = 'INCOME' AND date >= :startDate AND date <= :endDate")
    BigDecimal getTotalIncome(long startDate, long endDate);

    /**
     * 获取支出总额
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 支出总额
     */
    @Query("SELECT SUM(amount) FROM transactions WHERE type = 'EXPENSE' AND date >= :startDate AND date <= :endDate")
    BigDecimal getTotalExpense(long startDate, long endDate);

    /**
     * 获取收入总额（LiveData）
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 收入总额LiveData
     */
    @Query("SELECT SUM(amount) FROM transactions WHERE type = 'INCOME' AND date >= :startDate AND date <= :endDate")
    LiveData<BigDecimal> getTotalIncomeLiveData(long startDate, long endDate);

    /**
     * 获取支出总额（LiveData）
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 支出总额LiveData
     */
    @Query("SELECT SUM(amount) FROM transactions WHERE type = 'EXPENSE' AND date >= :startDate AND date <= :endDate")
    LiveData<BigDecimal> getTotalExpenseLiveData(long startDate, long endDate);

    /**
     * 获取所有时间收入总额（LiveData）
     * @return 收入总额LiveData
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'INCOME'")
    LiveData<BigDecimal> getAllTimeIncomeLiveData();

    /**
     * 获取所有时间支出总额（LiveData）
     * @return 支出总额LiveData
     */
    @Query("SELECT COALESCE(SUM(amount), 0) FROM transactions WHERE type = 'EXPENSE'")
    LiveData<BigDecimal> getAllTimeExpenseLiveData();

    /**
     * 根据分类统计支出
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 分类支出统计
     */
    @Query("SELECT category_id as categoryId, SUM(amount) as totalAmount FROM transactions WHERE type = 'EXPENSE' AND date >= :startDate AND date <= :endDate GROUP BY category_id ORDER BY totalAmount DESC")
    List<CategoryExpenseStatistic> getExpenseStatisticsByCategory(long startDate, long endDate);

    /**
     * 插入交易记录
     * @param transaction 交易记录
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertTransaction(Transaction transaction);

    /**
     * 批量插入交易记录
     * @param transactions 交易记录列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertTransactions(List<Transaction> transactions);

    /**
     * 更新交易记录
     * @param transaction 交易记录
     */
    @Update
    void updateTransaction(Transaction transaction);

    /**
     * 删除交易记录
     * @param transaction 交易记录
     */
    @Delete
    void deleteTransaction(Transaction transaction);

    /**
     * 根据ID删除交易记录
     * @param transactionId 交易ID
     */
    @Query("DELETE FROM transactions WHERE id = :transactionId")
    void deleteTransactionById(String transactionId);

    /**
     * 获取交易记录数量
     * @return 交易记录数量
     */
    @Query("SELECT COUNT(*) FROM transactions")
    int getTransactionCount();

    /**
     * 搜索交易记录
     * @param keyword 关键词
     * @return 交易记录列表
     */
    @Query("SELECT * FROM transactions WHERE description LIKE '%' || :keyword || '%' OR note LIKE '%' || :keyword || '%' ORDER BY date DESC, created_at DESC")
    List<Transaction> searchTransactions(String keyword);

    /**
     * 获取所有交易记录（带详情）LiveData
     */
    @Query("SELECT * FROM transactions ORDER BY date DESC, created_at DESC")
    LiveData<List<TransactionWithDetails>> getAllTransactionsWithDetailsLiveData();

    /**
     * 根据日期范围获取交易记录（带详情）LiveData
     */
    @Query("SELECT * FROM transactions WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, created_at DESC")
    LiveData<List<TransactionWithDetails>> getTransactionsWithDetailsByDateRangeLiveData(long startDate, long endDate);

    /**
     * 根据日期范围和类型获取交易记录（带详情）LiveData
     */
    @Query("SELECT * FROM transactions WHERE date >= :startDate AND date <= :endDate AND type = :type ORDER BY date DESC, created_at DESC")
    LiveData<List<TransactionWithDetails>> getTransactionsWithDetailsByDateRangeAndTypeLiveData(long startDate, long endDate, TransactionType type);

    /**
     * 分类支出统计结果类
     */
    public static class CategoryExpenseStatistic {
        public String categoryId;
        public BigDecimal totalAmount;

        public CategoryExpenseStatistic() {}

        public CategoryExpenseStatistic(String categoryId, BigDecimal totalAmount) {
            this.categoryId = categoryId;
            this.totalAmount = totalAmount;
        }
    }
}
