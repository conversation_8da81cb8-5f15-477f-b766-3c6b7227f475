package com.photo.restore.bookkeeping.data.converter;

import androidx.room.TypeConverter;

import com.photo.restore.bookkeeping.data.enums.AccountType;
import com.photo.restore.bookkeeping.data.enums.BudgetPeriod;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Room数据库类型转换器
 */
public class Converters {

    // Date转换器
    @TypeConverter
    public static Date fromTimestamp(Long value) {
        return value == null ? null : new Date(value);
    }

    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }

    // BigDecimal转换器
    @TypeConverter
    public static BigDecimal fromString(String value) {
        return value == null ? null : new BigDecimal(value);
    }

    @TypeConverter
    public static String bigDecimalToString(BigDecimal decimal) {
        return decimal == null ? null : decimal.toString();
    }

    // TransactionType转换器
    @TypeConverter
    public static TransactionType fromTransactionTypeString(String value) {
        if (value == null) {
            return null;
        }
        try {
            return TransactionType.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @TypeConverter
    public static String transactionTypeToString(TransactionType type) {
        return type == null ? null : type.name();
    }

    // AccountType转换器
    @TypeConverter
    public static AccountType fromAccountTypeString(String value) {
        if (value == null) {
            return null;
        }
        try {
            return AccountType.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @TypeConverter
    public static String accountTypeToString(AccountType type) {
        return type == null ? null : type.name();
    }

    // BudgetPeriod转换器
    @TypeConverter
    public static BudgetPeriod fromBudgetPeriodString(String value) {
        if (value == null) {
            return null;
        }
        try {
            return BudgetPeriod.valueOf(value);
        } catch (IllegalArgumentException e) {
            return null;
        }
    }

    @TypeConverter
    public static String budgetPeriodToString(BudgetPeriod period) {
        return period == null ? null : period.name();
    }
}
