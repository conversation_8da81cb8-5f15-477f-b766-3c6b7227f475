package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.UserSettings;

/**
 * 用户设置数据访问对象
 */
@Dao
public interface UserSettingsDao {

    /**
     * 获取用户设置（LiveData）
     * @return 用户设置LiveData
     */
    @Query("SELECT * FROM user_settings WHERE id = 'default_user' LIMIT 1")
    LiveData<UserSettings> getUserSettingsLiveData();

    /**
     * 获取用户设置（同步）
     * @return 用户设置
     */
    @Query("SELECT * FROM user_settings WHERE id = 'default_user' LIMIT 1")
    UserSettings getUserSettings();

    /**
     * 插入用户设置
     * @param userSettings 用户设置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertUserSettings(UserSettings userSettings);

    /**
     * 更新用户设置
     * @param userSettings 用户设置
     */
    @Update
    void updateUserSettings(UserSettings userSettings);

    /**
     * 更新首选语言
     * @param language 语言代码
     */
    @Query("UPDATE user_settings SET preferred_language = :language, updated_at = :updatedAt WHERE id = 'default_user'")
    void updatePreferredLanguage(String language, long updatedAt);

    /**
     * 更新默认货币
     * @param currency 货币代码
     */
    @Query("UPDATE user_settings SET default_currency = :currency, updated_at = :updatedAt WHERE id = 'default_user'")
    void updateDefaultCurrency(String currency, long updatedAt);

    /**
     * 更新深色模式设置
     * @param enabled 是否启用深色模式
     */
    @Query("UPDATE user_settings SET dark_mode_enabled = :enabled, updated_at = :updatedAt WHERE id = 'default_user'")
    void updateDarkMode(boolean enabled, long updatedAt);

    /**
     * 更新生物识别认证设置
     * @param enabled 是否启用生物识别认证
     */
    @Query("UPDATE user_settings SET enable_biometric_auth = :enabled, updated_at = :updatedAt WHERE id = 'default_user'")
    void updateBiometricAuth(boolean enabled, long updatedAt);

    /**
     * 更新通知设置
     * @param enabled 是否启用通知
     */
    @Query("UPDATE user_settings SET enable_notifications = :enabled, updated_at = :updatedAt WHERE id = 'default_user'")
    void updateNotifications(boolean enabled, long updatedAt);

    /**
     * 检查用户设置是否存在
     * @return 存在返回1，不存在返回0
     */
    @Query("SELECT COUNT(*) FROM user_settings WHERE id = 'default_user'")
    int getUserSettingsCount();
}
