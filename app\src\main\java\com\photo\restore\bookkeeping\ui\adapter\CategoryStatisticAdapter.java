package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import android.util.Log;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Category;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 分类统计适配器
 */
public class CategoryStatisticAdapter extends RecyclerView.Adapter<CategoryStatisticAdapter.ViewHolder> {

    /**
     * 分类统计数据类
     */
    public static class CategoryStatistic {
        public Category category;
        public BigDecimal amount;
        public double percentage;

        public CategoryStatistic(Category category, BigDecimal amount, double percentage) {
            this.category = category;
            this.amount = amount;
            this.percentage = percentage;
        }
    }

    private List<CategoryStatistic> statistics;
    private final NumberFormat currencyFormat;
    private final OnCategoryClickListener clickListener;

    /**
     * 分类点击监听器接口
     */
    public interface OnCategoryClickListener {
        void onCategoryClick(CategoryStatistic statistic);
    }

    public CategoryStatisticAdapter(List<CategoryStatistic> statistics, OnCategoryClickListener clickListener) {
        this.statistics = statistics != null ? statistics : new ArrayList<>();
        this.clickListener = clickListener;
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        this.currencyFormat.setCurrency(Currency.getInstance("CNY"));
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_category_statistic, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        Log.d("CategoryStatisticAdapter", "Binding item at position: " + position + " of " + statistics.size());
        CategoryStatistic statistic = statistics.get(position);
        holder.bind(statistic);
    }

    @Override
    public int getItemCount() {
        return statistics.size();
    }

    /**
     * 更新统计数据
     */
    public void updateStatistics(List<CategoryStatistic> newStatistics) {
        this.statistics = newStatistics != null ? newStatistics : new ArrayList<>();
        Log.d("CategoryStatisticAdapter", "Updated statistics, new size: " + this.statistics.size());
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    class ViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView tvCategoryIcon;
        private final TextView tvCategoryName;
        private final ProgressBar progressAmount;
        private final TextView tvAmount;
        private final TextView tvPercentage;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvCategoryIcon = itemView.findViewById(R.id.tv_category_icon);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            progressAmount = itemView.findViewById(R.id.progress_amount);
            tvAmount = itemView.findViewById(R.id.tv_amount);
            tvPercentage = itemView.findViewById(R.id.tv_percentage);
            
            // 设置点击监听器
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null) {
                    clickListener.onCategoryClick(statistics.get(position));
                }
            });
        }

        public void bind(CategoryStatistic statistic) {
            Context context = itemView.getContext();
            
            // 设置分类名称
            tvCategoryName.setText(statistic.category.getName());
            
            // 设置分类图标（使用emoji或者图标字符）
            String iconText = getCategoryIcon(statistic.category.getIconName());
            tvCategoryIcon.setText(iconText);
            
            // 设置图标背景颜色
            if (statistic.category.getColor() != null && !statistic.category.getColor().isEmpty()) {
                try {
                    int color = android.graphics.Color.parseColor(statistic.category.getColor());
                    tvCategoryIcon.setBackgroundTintList(android.content.res.ColorStateList.valueOf(color));
                } catch (IllegalArgumentException e) {
                    // 如果颜色解析失败，使用默认颜色
                    tvCategoryIcon.setBackgroundTintList(
                        ContextCompat.getColorStateList(context, R.color.expense_color));
                }
            } else {
                tvCategoryIcon.setBackgroundTintList(
                    ContextCompat.getColorStateList(context, R.color.expense_color));
            }
            
            // 设置金额
            tvAmount.setText(currencyFormat.format(statistic.amount));
            
            // 设置百分比
            String percentageText = String.format(Locale.getDefault(), "%.1f%%", statistic.percentage);
            tvPercentage.setText(percentageText);
            
            // 设置进度条
            int progress = (int) Math.round(statistic.percentage);
            progressAmount.setProgress(progress);
        }

        /**
         * 根据图标名称获取对应的emoji或字符
         */
        private String getCategoryIcon(String iconName) {
            if (iconName == null || iconName.isEmpty()) {
                return "💰";
            }
            
            // 根据图标名称返回对应的emoji
            switch (iconName.toLowerCase()) {
                case "food":
                case "餐饮":
                    return "🍽️";
                case "transport":
                case "交通":
                    return "🚗";
                case "shopping":
                case "购物":
                    return "🛒";
                case "entertainment":
                case "娱乐":
                    return "🎬";
                case "medical":
                case "医疗":
                    return "🏥";
                case "education":
                case "教育":
                    return "📚";
                case "housing":
                case "住房":
                    return "🏠";
                case "utilities":
                case "水电":
                    return "💡";
                case "communication":
                case "通讯":
                    return "📱";
                case "clothing":
                case "服装":
                    return "👕";
                case "beauty":
                case "美容":
                    return "💄";
                case "sports":
                case "运动":
                    return "⚽";
                case "travel":
                case "旅行":
                    return "✈️";
                case "gift":
                case "礼品":
                    return "🎁";
                case "pet":
                case "宠物":
                    return "🐕";
                case "salary":
                case "工资":
                    return "💼";
                case "bonus":
                case "奖金":
                    return "🎉";
                case "investment":
                case "投资":
                    return "📈";
                case "part_time":
                case "兼职":
                    return "⏰";
                case "business":
                case "生意":
                    return "🏪";
                case "rent":
                case "租金":
                    return "🏘️";
                case "interest":
                case "利息":
                    return "🏦";
                case "dividend":
                case "分红":
                    return "💎";
                case "refund":
                case "退款":
                    return "↩️";
                case "lottery":
                case "彩票":
                    return "🎲";
                default:
                    return "💰";
            }
        }
    }
}
