package com.photo.restore.bookkeeping.data.database;

import android.content.Context;

import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;

import com.photo.restore.bookkeeping.data.converter.Converters;
import com.photo.restore.bookkeeping.data.dao.AccountDao;
import com.photo.restore.bookkeeping.data.dao.BudgetDao;
import com.photo.restore.bookkeeping.data.dao.CategoryDao;
import com.photo.restore.bookkeeping.data.dao.ExchangeRateDao;
import com.photo.restore.bookkeeping.data.dao.TransactionDao;
import com.photo.restore.bookkeeping.data.dao.TransferDao;
import com.photo.restore.bookkeeping.data.dao.UserSettingsDao;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.entity.Budget;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.entity.ExchangeRate;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.Transfer;
import com.photo.restore.bookkeeping.data.entity.UserSettings;

/**
 * Room数据库类
 */
@Database(
    entities = {
        UserSettings.class,
        Account.class,
        Category.class,
        Transaction.class,
        Budget.class,
        Transfer.class,
        ExchangeRate.class
    },
    version = 1,
    exportSchema = false
)
@TypeConverters({Converters.class})
public abstract class BookkeepingDatabase extends RoomDatabase {

    private static final String DATABASE_NAME = "bookkeeping_database";
    private static volatile BookkeepingDatabase INSTANCE;

    // DAO抽象方法
    public abstract UserSettingsDao userSettingsDao();
    public abstract AccountDao accountDao();
    public abstract CategoryDao categoryDao();
    public abstract TransactionDao transactionDao();
    public abstract BudgetDao budgetDao();
    public abstract TransferDao transferDao();
    public abstract ExchangeRateDao exchangeRateDao();

    /**
     * 获取数据库实例（单例模式）
     * @param context 上下文
     * @return 数据库实例
     */
    public static BookkeepingDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (BookkeepingDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            BookkeepingDatabase.class,
                            DATABASE_NAME
                    )
                    .addCallback(new DatabaseCallback())
                    .enableMultiInstanceInvalidation() // 启用多实例失效
                    .fallbackToDestructiveMigration() // 数据库升级时允许破坏性迁移
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 数据库回调类
     */
    private static class DatabaseCallback extends RoomDatabase.Callback {
        @Override
        public void onCreate(androidx.sqlite.db.SupportSQLiteDatabase db) {
            super.onCreate(db);
            // 数据库创建时的回调，可以在这里初始化默认数据
        }

        @Override
        public void onOpen(androidx.sqlite.db.SupportSQLiteDatabase db) {
            super.onOpen(db);
            // 数据库打开时的回调
        }
    }

    /**
     * 销毁数据库实例（用于测试）
     */
    public static void destroyInstance() {
        INSTANCE = null;
    }
}
