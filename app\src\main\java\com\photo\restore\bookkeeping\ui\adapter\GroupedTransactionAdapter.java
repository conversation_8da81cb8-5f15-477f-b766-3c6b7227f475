package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 按日期分组的交易列表适配器
 */
public class GroupedTransactionAdapter extends RecyclerView.Adapter<GroupedTransactionAdapter.DateGroupViewHolder> {

    private List<DateGroup> dateGroups;
    private final TransactionWithDetailsAdapter.OnTransactionClickListener clickListener;
    private final NumberFormat currencyFormat;
    private final SimpleDateFormat dateFormat;

    /**
     * 日期分组数据类
     */
    public static class DateGroup {
        public Date date;
        public List<TransactionWithDetails> transactions;
        public BigDecimal totalIncome;
        public BigDecimal totalExpense;

        public DateGroup(Date date) {
            this.date = date;
            this.transactions = new ArrayList<>();
            this.totalIncome = BigDecimal.ZERO;
            this.totalExpense = BigDecimal.ZERO;
        }

        public void addTransaction(TransactionWithDetails transaction) {
            transactions.add(transaction);
            
            switch (transaction.transaction.getType()) {
                case INCOME:
                    totalIncome = totalIncome.add(transaction.transaction.getAmount());
                    break;
                case EXPENSE:
                    totalExpense = totalExpense.add(transaction.transaction.getAmount());
                    break;
            }
        }
    }

    public GroupedTransactionAdapter(List<TransactionWithDetails> transactions, TransactionWithDetailsAdapter.OnTransactionClickListener clickListener) {
        this.clickListener = clickListener;
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        this.currencyFormat.setCurrency(Currency.getInstance("CNY"));
        this.dateFormat = new SimpleDateFormat("MM月dd日 EEEE", Locale.getDefault());
        
        groupTransactionsByDate(transactions);
    }

    /**
     * 按日期分组交易
     */
    private void groupTransactionsByDate(List<TransactionWithDetails> transactions) {
        Map<String, DateGroup> groupMap = new LinkedHashMap<>();
        SimpleDateFormat keyFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

        for (TransactionWithDetails transaction : transactions) {
            Date transactionDate = transaction.transaction.getDate();
            String dateKey = keyFormat.format(transactionDate);

            DateGroup group = groupMap.get(dateKey);
            if (group == null) {
                group = new DateGroup(transactionDate);
                groupMap.put(dateKey, group);
            }
            
            group.addTransaction(transaction);
        }

        this.dateGroups = new ArrayList<>(groupMap.values());
    }

    @NonNull
    @Override
    public DateGroupViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_date_group, parent, false);
        return new DateGroupViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DateGroupViewHolder holder, int position) {
        DateGroup dateGroup = dateGroups.get(position);
        holder.bind(dateGroup);
    }

    @Override
    public int getItemCount() {
        return dateGroups != null ? dateGroups.size() : 0;
    }

    /**
     * 更新交易列表
     */
    public void updateTransactions(List<TransactionWithDetails> newTransactions) {
        groupTransactionsByDate(newTransactions);
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    class DateGroupViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView tvDate;
        private final TextView tvTotalIncome;
        private final TextView tvTotalExpense;
        private final RecyclerView rvTransactions;
        private TransactionWithDetailsAdapter transactionAdapter;

        public DateGroupViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvDate = itemView.findViewById(R.id.tv_date);
            tvTotalIncome = itemView.findViewById(R.id.tv_total_income);
            tvTotalExpense = itemView.findViewById(R.id.tv_total_expense);
            rvTransactions = itemView.findViewById(R.id.rv_transactions);
            
            // 设置嵌套RecyclerView
            rvTransactions.setLayoutManager(new LinearLayoutManager(itemView.getContext()));
            rvTransactions.setNestedScrollingEnabled(false);
        }

        public void bind(DateGroup dateGroup) {
            Context context = itemView.getContext();
            
            // 设置日期
            tvDate.setText(dateFormat.format(dateGroup.date));
            
            // 设置收入总额
            if (dateGroup.totalIncome.compareTo(BigDecimal.ZERO) > 0) {
                tvTotalIncome.setText("收入 " + currencyFormat.format(dateGroup.totalIncome));
                tvTotalIncome.setTextColor(ContextCompat.getColor(context, R.color.income_color));
                tvTotalIncome.setVisibility(View.VISIBLE);
            } else {
                tvTotalIncome.setVisibility(View.GONE);
            }
            
            // 设置支出总额
            if (dateGroup.totalExpense.compareTo(BigDecimal.ZERO) > 0) {
                tvTotalExpense.setText("支出 " + currencyFormat.format(dateGroup.totalExpense));
                tvTotalExpense.setTextColor(ContextCompat.getColor(context, R.color.expense_color));
                tvTotalExpense.setVisibility(View.VISIBLE);
            } else {
                tvTotalExpense.setVisibility(View.GONE);
            }
            
            // 设置交易列表
            if (transactionAdapter == null) {
                transactionAdapter = new TransactionWithDetailsAdapter(dateGroup.transactions, clickListener);
                rvTransactions.setAdapter(transactionAdapter);
            } else {
                transactionAdapter.updateTransactions(dateGroup.transactions);
            }
        }
    }
}
