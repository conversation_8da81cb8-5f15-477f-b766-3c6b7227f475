buildscript {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath "com.android.tools.build:gradle:${Versions.gradle}"
        //noinspection DifferentKotlinGradleVersion
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${Versions.kotlin}"

        if (!Options.IS_PUBLISH) {
            // 类文件混淆插件
            classpath "com.gitee.tiamosu:AndroidGuard:3.2.9"
        }
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}