<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginBottom="8dp">

    <!-- 日期头部 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?attr/colorSurfaceVariant">

        <TextView
            android:id="@+id/tv_date"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textStyle="bold"
            android:textColor="?attr/colorOnSurfaceVariant"
            tools:text="12月25日 星期一" />

        <!-- 收支汇总 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <TextView
                android:id="@+id/tv_total_income"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="收入 ¥500.00"
                tools:textColor="@color/income_color"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tv_total_expense"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textAppearance="@style/TextAppearance.Material3.BodySmall"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="支出 ¥200.00"
                tools:textColor="@color/expense_color"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

    <!-- 交易列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_transactions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:nestedScrollingEnabled="false"
        android:background="?attr/colorSurface"
        tools:itemCount="3"
        tools:listitem="@layout/item_transaction" />

</LinearLayout>
