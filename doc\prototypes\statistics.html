<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 统计</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5B4BCF',
                        'secondary': '#A29BFE',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E84393',
                        'surface': '#FFFFFF',
                        'neutral-50': '#FAFBFC',
                        'neutral-100': '#F4F5F7',
                        'neutral-200': '#E3E5E8',
                        'neutral-300': '#CFD3D8',
                        'neutral-400': '#A6ACB5',
                        'neutral-500': '#7B8794',
                        'neutral-600': '#5A6575',
                        'neutral-700': '#3E4651',
                        'neutral-800': '#2D3843',
                        'neutral-900': '#1F2937'
                    },
                    fontFamily: {
                        'display': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'system-ui', 'sans-serif'],
                        'body': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'system-ui', 'sans-serif']
                    },
                    borderRadius: {
                        '2xl': '1rem',
                        '3xl': '1.5rem',
                        '4xl': '2rem'
                    },
                    boxShadow: {
                        'soft': '0 2px 12px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 24px rgba(0, 0, 0, 0.06)',
                        'large': '0 8px 40px rgba(0, 0, 0, 0.08)',
                        'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 16px rgba(0, 0, 0, 0.04)',
                        'button': '0 2px 8px rgba(108, 92, 231, 0.2)',
                        'floating': '0 8px 32px rgba(108, 92, 231, 0.15)',
                        'glow': '0 0 20px rgba(108, 92, 231, 0.3)'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(145deg, #F8F9FA 0%, #E9ECEF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        .glass-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .overview-card {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 50%, #FD79A8 100%);
            position: relative;
            overflow: hidden;
        }
        
        .overview-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .chart-container {
            position: relative;
            background: linear-gradient(145deg, #FFFFFF 0%, #F8F9FA 100%);
            border: 1px solid rgba(108, 92, 231, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .chart-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
        }
        
        .pie-chart {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: conic-gradient(
                #FDCB6E 0deg 133deg,
                #6C5CE7 133deg 205deg,
                #A29BFE 205deg 277deg,
                #00B894 277deg 325deg,
                #E84393 325deg 360deg
            );
            position: relative;
            box-shadow: 0 6px 24px rgba(108, 92, 231, 0.2);
        }
        
        .pie-chart::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: 50%;
            background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 50%);
        }
        
        .line-chart-point {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            filter: drop-shadow(0 2px 4px rgba(108, 92, 231, 0.2));
        }
        
        .line-chart-point:hover {
            transform: scale(1.3);
            filter: drop-shadow(0 4px 8px rgba(108, 92, 231, 0.4));
        }
        
        .trend-line {
            stroke-dasharray: 300;
            stroke-dashoffset: 300;
            animation: drawLine 2.5s ease-in-out forwards;
            filter: drop-shadow(0 2px 4px rgba(108, 92, 231, 0.1));
        }
        
        @keyframes drawLine {
            to {
                stroke-dashoffset: 0;
            }
        }
        
        .period-tab {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #FFFFFF 0%, #F8F9FA 100%);
            border: 1px solid rgba(108, 92, 231, 0.1);
        }
        
        .period-tab:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 92, 231, 0.1);
        }
        
        .period-tab.active {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
            transform: translateY(-1px);
        }
        
        .stat-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stat-item:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .category-legend {
            transition: all 0.2s ease;
        }
        
        .category-legend:hover {
            transform: translateX(4px);
            background: rgba(108, 92, 231, 0.05);
        }
        
        .floating-action-mini {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
            transition: all 0.3s ease;
        }
        
        .floating-action-mini:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(108, 92, 231, 0.4);
        }
        
        .icon-gradient {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-neutral-50 to-neutral-100 min-h-screen flex items-center justify-center font-body">
    <div class="phone-container">
        
        <!-- 状态栏 -->
        <div class="h-11 bg-transparent flex items-center justify-between px-6 text-neutral-800 text-sm font-medium">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-three-quarters text-xs"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="glass-card h-16 flex items-center justify-between px-4 mb-2 mx-4 rounded-2xl shadow-card">
            <h1 class="font-bold text-xl text-neutral-800 font-display">统计分析</h1>
            <div class="flex items-center space-x-2">
                <button class="floating-action-mini p-2 rounded-xl">
                    <i class="fas fa-download text-white text-sm"></i>
                </button>
                <button class="floating-action-mini p-2 rounded-xl">
                    <i class="fas fa-share text-white text-sm"></i>
                </button>
            </div>
        </div>
        
        <!-- 时间选择 -->
        <div class="px-4 mb-4">
            <div class="glass-card rounded-2xl p-4 shadow-card">
                <div class="flex justify-center space-x-2">
                    <button class="period-tab active px-6 py-3 rounded-xl text-sm font-semibold">本月</button>
                    <button class="period-tab px-6 py-3 rounded-xl text-sm font-semibold text-neutral-600">本年</button>
                    <button class="period-tab px-6 py-3 rounded-xl text-sm font-semibold text-neutral-600">自定义</button>
                </div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="flex-1 overflow-y-auto px-4 space-y-4">
            
            <!-- 收支概览 -->
            <div class="overview-card rounded-3xl p-6 shadow-floating relative">
                <div class="relative z-10">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="font-bold text-white text-lg font-display">2024年6月概览</h3>
                        <button class="glass-card px-4 py-2 rounded-xl text-white text-sm font-semibold border border-white/20">
                            <i class="fas fa-calendar-alt mr-2"></i>6月
                        </button>
                    </div>
                    <div class="grid grid-cols-3 gap-3 mb-6">
                        <div class="stat-item text-center p-3 rounded-2xl">
                            <div class="w-4 h-4 bg-gradient-to-r from-success to-emerald-400 rounded-full mx-auto mb-2"></div>
                            <p class="text-success text-base font-bold font-display">¥8,500</p>
                            <p class="text-xs text-neutral-600 font-medium">收入</p>
                        </div>
                        <div class="stat-item text-center p-3 rounded-2xl">
                            <div class="w-4 h-4 bg-gradient-to-r from-danger to-pink-400 rounded-full mx-auto mb-2"></div>
                            <p class="text-danger text-base font-bold font-display">¥3,240</p>
                            <p class="text-xs text-neutral-600 font-medium">支出</p>
                        </div>
                        <div class="stat-item text-center p-3 rounded-2xl">
                            <div class="w-4 h-4 bg-gradient-to-r from-primary to-secondary rounded-full mx-auto mb-2"></div>
                            <p class="text-primary text-base font-bold font-display">¥5,260</p>
                            <p class="text-xs text-neutral-600 font-medium">结余</p>
                        </div>
                    </div>
                    <!-- 趋势图 -->
                    <div class="glass-card h-24 rounded-2xl relative overflow-hidden p-4">
                        <svg class="w-full h-full" viewBox="0 0 300 80">
                            <defs>
                                <linearGradient id="incomeGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#00B894;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#00B894;stop-opacity:0" />
                                </linearGradient>
                                <linearGradient id="expenseGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                    <stop offset="0%" style="stop-color:#E84393;stop-opacity:0.4" />
                                    <stop offset="100%" style="stop-color:#E84393;stop-opacity:0" />
                                </linearGradient>
                            </defs>
                            <!-- 网格线 -->
                            <g stroke="rgba(108, 92, 231, 0.1)" stroke-width="0.5">
                                <line x1="0" y1="20" x2="300" y2="20"/>
                                <line x1="0" y1="40" x2="300" y2="40"/>
                                <line x1="0" y1="60" x2="300" y2="60"/>
                            </g>
                            <!-- 收入趋势线 -->
                            <polyline class="trend-line" fill="none" stroke="#00B894" stroke-width="3" points="20,60 60,45 100,50 140,35 180,40 220,30 260,25"/>
                            <polygon fill="url(#incomeGradient)" points="20,60 60,45 100,50 140,35 180,40 220,30 260,25 260,80 20,80"/>
                            <!-- 支出趋势线 -->
                            <polyline class="trend-line" fill="none" stroke="#E84393" stroke-width="3" points="20,70 60,65 100,55 140,60 180,50 220,55 260,45"/>
                            <polygon fill="url(#expenseGradient)" points="20,70 60,65 100,55 140,60 180,50 220,55 260,45 260,80 20,80"/>
                            <!-- 数据点 -->
                            <circle class="line-chart-point" cx="20" cy="60" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="60" cy="45" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="100" cy="50" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="140" cy="35" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="180" cy="40" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="220" cy="30" r="4" fill="#00B894"/>
                            <circle class="line-chart-point" cx="260" cy="25" r="4" fill="#00B894"/>
                            
                            <circle class="line-chart-point" cx="20" cy="70" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="60" cy="65" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="100" cy="55" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="140" cy="60" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="180" cy="50" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="220" cy="55" r="4" fill="#E84393"/>
                            <circle class="line-chart-point" cx="260" cy="45" r="4" fill="#E84393"/>
                        </svg>
                    </div>
                </div>
            
            <!-- 支出分析 -->
            <div class="chart-container rounded-3xl p-5 shadow-card">
                <h3 class="font-bold mb-5 text-neutral-800 text-base font-display">支出分析</h3>
                <div class="flex items-start space-x-4">
                    <!-- 饼图 -->
                    <div class="flex-shrink-0">
                        <div class="pie-chart relative" style="width: 140px; height: 140px;">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-lg">
                                    <div class="text-center">
                                        <p class="text-xs text-neutral-500 font-medium">总支出</p>
                                        <p class="text-xs font-bold text-neutral-800 font-display">¥3,240</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 分类列表 -->
                    <div class="flex-1 min-w-0 space-y-3">
                        <div class="category-legend flex items-center justify-between p-2 rounded-xl">
                            <div class="flex items-center min-w-0">
                                <div class="w-3 h-3 bg-gradient-to-r from-warning to-orange-400 rounded-full mr-2 flex-shrink-0"></div>
                                <span class="text-xs font-medium text-neutral-700 truncate">餐饮</span>
                            </div>
                            <div class="text-right flex-shrink-0 ml-2">
                                <p class="text-xs font-bold text-neutral-800">¥1,200</p>
                                <p class="text-xs text-neutral-500">37%</p>
                            </div>
                        </div>
                        <div class="category-legend flex items-center justify-between p-2 rounded-xl">
                            <div class="flex items-center min-w-0">
                                <div class="w-3 h-3 bg-gradient-to-r from-primary to-secondary rounded-full mr-2 flex-shrink-0"></div>
                                <span class="text-xs font-medium text-neutral-700 truncate">交通</span>
                            </div>
                            <div class="text-right flex-shrink-0 ml-2">
                                <p class="text-xs font-bold text-neutral-800">¥640</p>
                                <p class="text-xs text-neutral-500">20%</p>
                            </div>
                        </div>
                        <div class="category-legend flex items-center justify-between p-2 rounded-xl">
                            <div class="flex items-center min-w-0">
                                <div class="w-3 h-3 bg-gradient-to-r from-secondary to-accent rounded-full mr-2 flex-shrink-0"></div>
                                <span class="text-xs font-medium text-neutral-700 truncate">娱乐</span>
                            </div>
                            <div class="text-right flex-shrink-0 ml-2">
                                <p class="text-xs font-bold text-neutral-800">¥800</p>
                                <p class="text-xs text-neutral-500">25%</p>
                            </div>
                        </div>
                        <div class="category-legend flex items-center justify-between p-2 rounded-xl">
                            <div class="flex items-center min-w-0">
                                <div class="w-3 h-3 bg-gradient-to-r from-success to-emerald-400 rounded-full mr-2 flex-shrink-0"></div>
                                <span class="text-xs font-medium text-neutral-700 truncate">购物</span>
                            </div>
                            <div class="text-right flex-shrink-0 ml-2">
                                <p class="text-xs font-bold text-neutral-800">¥420</p>
                                <p class="text-xs text-neutral-500">13%</p>
                            </div>
                        </div>
                        <div class="category-legend flex items-center justify-between p-2 rounded-xl">
                            <div class="flex items-center min-w-0">
                                <div class="w-3 h-3 bg-gradient-to-r from-danger to-pink-400 rounded-full mr-2 flex-shrink-0"></div>
                                <span class="text-xs font-medium text-neutral-700 truncate">其他</span>
                            </div>
                            <div class="text-right flex-shrink-0 ml-2">
                                <p class="text-xs font-bold text-neutral-800">¥180</p>
                                <p class="text-xs text-neutral-500">5%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 日均支出 -->
            <div class="chart-container rounded-3xl p-6 shadow-card mb-20">
                <h3 class="font-bold mb-6 text-neutral-800 text-lg font-display">每日支出趋势</h3>
                <div class="flex items-end justify-between h-32 mb-4 px-2">
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-primary to-secondary rounded-t-lg shadow-sm" style="height: 75px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周一</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-warning to-orange-400 rounded-t-lg shadow-sm" style="height: 50px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周二</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-success to-emerald-400 rounded-t-lg shadow-sm" style="height: 100px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周三</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-accent to-pink-400 rounded-t-lg shadow-sm" style="height: 56px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周四</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-danger to-pink-500 rounded-t-lg shadow-sm" style="height: 112px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周五</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-secondary to-primary rounded-t-lg shadow-sm" style="height: 81px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周六</span>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 bg-gradient-to-t from-warning to-orange-300 rounded-t-lg shadow-sm" style="height: 44px;"></div>
                        <span class="text-xs text-neutral-500 mt-2 font-medium">周日</span>
                    </div>
                </div>
                <div class="flex justify-between text-xs text-neutral-500 font-medium px-2">
                    <span>¥120</span>
                    <span>¥80</span>
                    <span>¥160</span>
                    <span>¥90</span>
                    <span>¥180</span>
                    <span>¥130</span>
                    <span>¥70</span>
                </div>
            </div>
            
            <!-- 账户分析 -->
            <div class="chart-container rounded-3xl p-6 shadow-card">
                <h3 class="font-bold mb-6 text-neutral-800 text-lg font-display">账户使用情况</h3>
                <div class="space-y-4">
                    <div class="category-legend flex items-center justify-between p-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-2xl mr-4 shadow-button icon-gradient">
                                <i class="fas fa-credit-card text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-bold text-neutral-800">招商银行</p>
                                <p class="text-xs text-neutral-500 font-medium">使用频率最高</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 text-base">65%</p>
                            <p class="text-xs text-neutral-500">¥2,106</p>
                        </div>
                    </div>
                    <div class="category-legend flex items-center justify-between p-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-secondary to-accent rounded-2xl mr-4 shadow-button icon-gradient">
                                <i class="fab fa-alipay text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-bold text-neutral-800">支付宝</p>
                                <p class="text-xs text-neutral-500 font-medium">日常小额支付</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 text-base">25%</p>
                            <p class="text-xs text-neutral-500">¥810</p>
                        </div>
                    </div>
                    <div class="category-legend flex items-center justify-between p-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-success to-emerald-500 rounded-2xl mr-4 shadow-button icon-gradient">
                                <i class="fas fa-wallet text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="font-bold text-neutral-800">现金</p>
                                <p class="text-xs text-neutral-500 font-medium">应急备用</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="font-bold text-neutral-800 text-base">10%</p>
                            <p class="text-xs text-neutral-500">¥324</p>
                        </div>
                    </div>
                </div>
            </div>
                
            </div>
            
            <!-- 底部间距 -->
            <div class="h-20"></div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="absolute bottom-0 left-0 right-0 h-20 glass-card border-t border-white/20 flex">
            <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-200 hover:text-primary">
                <i class="fas fa-home text-lg mb-1"></i>
                <span class="text-xs font-medium">首页</span>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-200 hover:text-primary">
                <i class="fas fa-list text-lg mb-1"></i>
                <span class="text-xs font-medium">账单</span>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-primary relative">
                <i class="fas fa-chart-pie text-lg mb-1"></i>
                <span class="text-xs font-semibold">统计</span>
                <div class="absolute -top-1 w-6 h-1 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
            </button>
            <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-200 hover:text-primary">
                <i class="fas fa-cog text-lg mb-1"></i>
                <span class="text-xs font-medium">设置</span>
            </button>
        </div>
        
    </div>

    <script>
        // 时间选择标签切换
        document.querySelectorAll('.period-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.period-tab').forEach(t => {
                    t.classList.remove('active');
                    t.classList.add('text-neutral-600');
                });
                this.classList.add('active');
                this.classList.remove('text-neutral-600');
            });
        });
        
        // 图表数据点交互
        document.querySelectorAll('.line-chart-point').forEach(point => {
            point.addEventListener('mouseenter', function() {
                this.style.r = '6';
            });
            point.addEventListener('mouseleave', function() {
                this.style.r = '4';
            });
        });
    </script>
</body>
</html> 