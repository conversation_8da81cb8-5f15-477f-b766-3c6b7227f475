package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 包含详细信息的交易记录适配器
 */
public class TransactionWithDetailsAdapter extends RecyclerView.Adapter<TransactionWithDetailsAdapter.TransactionViewHolder> {

    private List<TransactionWithDetails> transactions;
    private final OnTransactionClickListener clickListener;
    private final NumberFormat currencyFormat;
    private final SimpleDateFormat timeFormat;

    /**
     * 交易点击监听器接口
     */
    public interface OnTransactionClickListener {
        void onTransactionClick(TransactionWithDetails transactionWithDetails);
    }

    public TransactionWithDetailsAdapter(List<TransactionWithDetails> transactions, OnTransactionClickListener clickListener) {
        this.transactions = transactions;
        this.clickListener = clickListener;
        
        // 初始化格式化器
        this.currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        this.currencyFormat.setCurrency(Currency.getInstance("CNY"));
        this.timeFormat = new SimpleDateFormat("HH:mm", Locale.getDefault());
    }

    @NonNull
    @Override
    public TransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_transaction, parent, false);
        return new TransactionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TransactionViewHolder holder, int position) {
        TransactionWithDetails transactionWithDetails = transactions.get(position);
        holder.bind(transactionWithDetails);
    }

    @Override
    public int getItemCount() {
        return transactions != null ? transactions.size() : 0;
    }

    /**
     * 更新交易列表
     */
    public void updateTransactions(List<TransactionWithDetails> newTransactions) {
        this.transactions = newTransactions;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    class TransactionViewHolder extends RecyclerView.ViewHolder {
        
        private final TextView tvCategoryIcon;
        private final TextView tvDescription;
        private final TextView tvCategory;
        private final TextView tvAccount;
        private final TextView tvAmount;
        private final TextView tvTime;

        public TransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvCategoryIcon = itemView.findViewById(R.id.tv_category_icon);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvCategory = itemView.findViewById(R.id.tv_category);
            tvAccount = itemView.findViewById(R.id.tv_account);
            tvAmount = itemView.findViewById(R.id.tv_amount);
            tvTime = itemView.findViewById(R.id.tv_time);
            
            // 设置点击监听器
            itemView.setOnClickListener(v -> {
                int position = getAdapterPosition();
                if (position != RecyclerView.NO_POSITION && clickListener != null) {
                    clickListener.onTransactionClick(transactions.get(position));
                }
            });
        }

        public void bind(TransactionWithDetails transactionWithDetails) {
            Context context = itemView.getContext();
            
            if (transactionWithDetails == null || transactionWithDetails.transaction == null) {
                return;
            }
            
            // 设置描述
            String description = transactionWithDetails.transaction.getDescription();
            tvDescription.setText(description != null && !description.isEmpty() 
                ? description 
                : getDefaultDescription(transactionWithDetails.transaction.getType()));
            
            // 设置分类图标和名称
            if (transactionWithDetails.category != null) {
                // 使用分类的图标，如果没有则使用emoji
                String iconName = transactionWithDetails.category.getIconName();
                if (iconName != null && !iconName.isEmpty()) {
                    tvCategoryIcon.setText(getCategoryEmoji(iconName));
                } else {
                    tvCategoryIcon.setText(getDefaultCategoryIcon(transactionWithDetails.transaction.getType()));
                }
                tvCategory.setText(transactionWithDetails.category.getName());
            } else {
                tvCategoryIcon.setText(getDefaultCategoryIcon(transactionWithDetails.transaction.getType()));
                tvCategory.setText("未分类");
            }
            
            // 设置账户名称
            if (transactionWithDetails.account != null) {
                tvAccount.setText(transactionWithDetails.account.getName());
            } else {
                tvAccount.setText("未知账户");
            }
            
            // 设置金额
            BigDecimal amount = transactionWithDetails.transaction.getAmount();
            String amountText;
            int amountColor;
            
            if (transactionWithDetails.transaction.getType() == TransactionType.INCOME) {
                amountText = "+" + currencyFormat.format(amount);
                amountColor = ContextCompat.getColor(context, R.color.income_color);
            } else {
                amountText = "-" + currencyFormat.format(amount);
                amountColor = ContextCompat.getColor(context, R.color.expense_color);
            }
            
            tvAmount.setText(amountText);
            tvAmount.setTextColor(amountColor);
            
            // 设置时间
            if (transactionWithDetails.transaction.getDate() != null) {
                tvTime.setText(timeFormat.format(transactionWithDetails.transaction.getDate()));
            } else {
                tvTime.setText("");
            }
        }

        /**
         * 获取默认描述
         */
        private String getDefaultDescription(TransactionType type) {
            Context context = itemView.getContext();
            if (type == TransactionType.INCOME) {
                return context.getString(R.string.income);
            } else {
                return context.getString(R.string.expense);
            }
        }

        /**
         * 获取默认分类图标
         */
        private String getDefaultCategoryIcon(TransactionType type) {
            if (type == TransactionType.INCOME) {
                return "💰"; // 收入默认图标
            } else {
                return "🛒"; // 支出默认图标
            }
        }

        /**
         * 根据图标名称获取对应的emoji
         */
        private String getCategoryEmoji(String iconName) {
            switch (iconName.toLowerCase()) {
                case "food":
                case "餐饮":
                    return "🍽️";
                case "transport":
                case "交通":
                    return "🚗";
                case "shopping":
                case "购物":
                    return "🛒";
                case "entertainment":
                case "娱乐":
                    return "🎬";
                case "health":
                case "医疗":
                    return "🏥";
                case "education":
                case "教育":
                    return "📚";
                case "salary":
                case "工资":
                    return "💼";
                case "investment":
                case "投资":
                    return "📈";
                case "gift":
                case "礼品":
                    return "🎁";
                case "home":
                case "居家":
                    return "🏠";
                default:
                    return "💰";
            }
        }
    }
}
