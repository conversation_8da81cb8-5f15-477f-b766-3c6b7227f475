package com.photo.restore.bookkeeping.data;

import android.app.Application;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.data.repository.CategoryRepository;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据初始化类
 * 用于创建默认的分类数据
 */
public class DataInitializer {

    private final CategoryRepository categoryRepository;
    private final ExecutorService executor;
    private final Application application;

    public DataInitializer(Application application) {
        this.application = application;
        categoryRepository = new CategoryRepository(application);
        executor = Executors.newSingleThreadExecutor();
    }

    /**
     * 初始化默认分类数据
     */
    public void initializeDefaultCategories() {
        executor.execute(() -> {
            // 只创建默认分类，不创建模拟数据
            createDefaultCategories();
        });
    }

    /**
     * 创建默认分类
     */
    private void createDefaultCategories() {
        // 检查是否已经初始化过系统默认分类
        List<Category> existingSystemCategories = categoryRepository.getSystemDefaultCategories();
        if (!existingSystemCategories.isEmpty()) {
            // 已经有系统默认分类，跳过初始化
            return;
        }

        // 支出分类
        createCategory("expense-food", application.getString(R.string.category_food), TransactionType.EXPENSE, "food", "#FF6B6B");
        createCategory("expense-transport", application.getString(R.string.category_transport), TransactionType.EXPENSE, "transport", "#4ECDC4");
        createCategory("expense-shopping", application.getString(R.string.category_shopping), TransactionType.EXPENSE, "shopping", "#45B7D1");
        createCategory("expense-entertainment", application.getString(R.string.category_entertainment), TransactionType.EXPENSE, "entertainment", "#96CEB4");
        createCategory("expense-health", application.getString(R.string.category_health), TransactionType.EXPENSE, "health", "#FFEAA7");
        createCategory("expense-education", application.getString(R.string.category_education), TransactionType.EXPENSE, "education", "#DDA0DD");
        createCategory("expense-home", application.getString(R.string.category_housing), TransactionType.EXPENSE, "home", "#98D8C8");

        // 收入分类
        createCategory("income-salary", application.getString(R.string.category_salary), TransactionType.INCOME, "salary", "#55A3FF");
        createCategory("income-bonus", application.getString(R.string.category_bonus), TransactionType.INCOME, "gift", "#FFD93D");
        createCategory("income-investment", application.getString(R.string.category_investment), TransactionType.INCOME, "investment", "#6BCF7F");
        createCategory("income-other", application.getString(R.string.category_other_income), TransactionType.INCOME, "gift", "#A8E6CF");
    }

    /**
     * 创建分类
     */
    private void createCategory(String id, String name, TransactionType type, String iconName, String color) {
        Category category = new Category();
        category.setId(id);
        category.setName(name);
        category.setType(type);
        category.setIconName(iconName);
        category.setColor(color);
        category.setSystemDefault(true);
        category.setActive(true);
        categoryRepository.insertCategory(category);
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
