package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Category;

import java.util.List;

/**
 * 分类下拉列表适配器
 */
public class CategorySpinnerAdapter extends ArrayAdapter<Category> {

    private final LayoutInflater inflater;
    private List<Category> categories;

    public CategorySpinnerAdapter(@NonNull Context context, @NonNull List<Category> categories) {
        super(context, R.layout.item_spinner_category, categories);
        this.inflater = LayoutInflater.from(context);
        this.categories = categories;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return createView(position, convertView, parent);
    }

    @Override
    public View getDropDownView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        return createView(position, convertView, parent);
    }

    private View createView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.item_spinner_category, parent, false);
            holder = new ViewHolder();
            holder.tvCategoryIcon = convertView.findViewById(R.id.tv_category_icon);
            holder.tvCategoryName = convertView.findViewById(R.id.tv_category_name);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }

        Category category = categories.get(position);
        if (category != null) {
            // 设置分类图标
            String iconName = category.getIconName();
            if (iconName != null && !iconName.isEmpty()) {
                holder.tvCategoryIcon.setText(getCategoryEmoji(iconName));
            } else {
                holder.tvCategoryIcon.setText("💰");
            }
            
            // 设置分类名称
            holder.tvCategoryName.setText(category.getName());
        }

        return convertView;
    }

    /**
     * 更新分类列表
     */
    public void updateCategories(List<Category> newCategories) {
        this.categories = newCategories;
        clear();
        addAll(newCategories);
        notifyDataSetChanged();
    }

    /**
     * 根据图标名称获取对应的emoji
     */
    private String getCategoryEmoji(String iconName) {
        switch (iconName.toLowerCase()) {
            case "food":
                return "🍽️";
            case "transport":
                return "🚗";
            case "shopping":
                return "🛒";
            case "entertainment":
                return "🎬";
            case "health":
                return "🏥";
            case "education":
                return "📚";
            case "salary":
                return "💼";
            case "investment":
                return "📈";
            case "gift":
                return "🎁";
            case "home":
                return "🏠";
            case "phone":
                return "📱";
            case "work":
                return "💼";
            case "other":
                return "📝";
            default:
                return "💰";
        }
    }

    /**
     * ViewHolder类
     */
    private static class ViewHolder {
        TextView tvCategoryIcon;
        TextView tvCategoryName;
    }
}
