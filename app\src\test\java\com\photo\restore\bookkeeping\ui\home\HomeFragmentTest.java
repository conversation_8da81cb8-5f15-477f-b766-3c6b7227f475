package com.photo.restore.bookkeeping.ui.home;

import static org.junit.Assert.*;

import org.junit.Before;
import org.junit.Test;

/**
 * HomeFragment的单元测试
 */
public class HomeFragmentTest {

    private HomeFragment fragment;

    @Before
    public void setUp() {
        fragment = new HomeFragment();
    }

    @Test
    public void testFragmentCreation() {
        assertNotNull("Fragment应该能够成功创建", fragment);
    }

    @Test
    public void testFragmentIsNotNull() {
        HomeFragment homeFragment = new HomeFragment();
        assertNotNull("HomeFragment实例不应该为null", homeFragment);
    }
}
