package com.photo.restore.bookkeeping.data.enums;

/**
 * 预算周期枚举
 */
public enum BudgetPeriod {
    /**
     * 月度
     */
    MONTHLY("月度", "Monthly"),
    
    /**
     * 年度
     */
    YEARLY("年度", "Yearly");

    private final String chineseName;
    private final String englishName;

    BudgetPeriod(String chineseName, String englishName) {
        this.chineseName = chineseName;
        this.englishName = englishName;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    /**
     * 根据语言获取显示名称
     * @param isEnglish 是否为英文
     * @return 显示名称
     */
    public String getDisplayName(boolean isEnglish) {
        return isEnglish ? englishName : chineseName;
    }
}
