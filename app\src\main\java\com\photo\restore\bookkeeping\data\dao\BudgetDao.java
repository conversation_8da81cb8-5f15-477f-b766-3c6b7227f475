package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.Budget;
import com.photo.restore.bookkeeping.data.enums.BudgetPeriod;

import java.util.List;

/**
 * 预算数据访问对象
 */
@Dao
public interface BudgetDao {

    /**
     * 获取所有活跃预算（LiveData）
     * @return 预算列表LiveData
     */
    @Query("SELECT * FROM budgets WHERE is_active = 1 ORDER BY created_at DESC")
    LiveData<List<Budget>> getAllActiveBudgetsLiveData();

    /**
     * 获取所有活跃预算（同步）
     * @return 预算列表
     */
    @Query("SELECT * FROM budgets WHERE is_active = 1 ORDER BY created_at DESC")
    List<Budget> getAllActiveBudgets();

    /**
     * 根据分类获取预算
     * @param categoryId 分类ID
     * @return 预算列表
     */
    @Query("SELECT * FROM budgets WHERE category_id = :categoryId AND is_active = 1 ORDER BY created_at DESC")
    List<Budget> getBudgetsByCategory(String categoryId);

    /**
     * 根据周期获取预算
     * @param period 预算周期
     * @return 预算列表
     */
    @Query("SELECT * FROM budgets WHERE period = :period AND is_active = 1 ORDER BY created_at DESC")
    List<Budget> getBudgetsByPeriod(BudgetPeriod period);

    /**
     * 根据日期范围获取有效预算
     * @param date 日期（时间戳）
     * @return 预算列表
     */
    @Query("SELECT * FROM budgets WHERE is_active = 1 AND start_date <= :date AND end_date >= :date ORDER BY created_at DESC")
    List<Budget> getValidBudgetsForDate(long date);

    /**
     * 根据日期范围获取有效预算（LiveData）
     * @param date 日期（时间戳）
     * @return 预算列表LiveData
     */
    @Query("SELECT * FROM budgets WHERE is_active = 1 AND start_date <= :date AND end_date >= :date ORDER BY created_at DESC")
    LiveData<List<Budget>> getValidBudgetsForDateLiveData(long date);

    /**
     * 根据ID获取预算
     * @param budgetId 预算ID
     * @return 预算
     */
    @Query("SELECT * FROM budgets WHERE id = :budgetId")
    Budget getBudgetById(String budgetId);

    /**
     * 根据ID获取预算（LiveData）
     * @param budgetId 预算ID
     * @return 预算LiveData
     */
    @Query("SELECT * FROM budgets WHERE id = :budgetId")
    LiveData<Budget> getBudgetByIdLiveData(String budgetId);

    /**
     * 获取启用通知的预算
     * @return 预算列表
     */
    @Query("SELECT * FROM budgets WHERE is_active = 1 AND notification_enabled = 1 ORDER BY created_at DESC")
    List<Budget> getBudgetsWithNotificationEnabled();

    /**
     * 插入预算
     * @param budget 预算
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertBudget(Budget budget);

    /**
     * 批量插入预算
     * @param budgets 预算列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertBudgets(List<Budget> budgets);

    /**
     * 更新预算
     * @param budget 预算
     */
    @Update
    void updateBudget(Budget budget);

    /**
     * 软删除预算（设置为非活跃）
     * @param budgetId 预算ID
     * @param updatedAt 更新时间
     */
    @Query("UPDATE budgets SET is_active = 0, updated_at = :updatedAt WHERE id = :budgetId")
    void softDeleteBudget(String budgetId, long updatedAt);

    /**
     * 删除预算
     * @param budget 预算
     */
    @Delete
    void deleteBudget(Budget budget);

    /**
     * 获取预算数量
     * @return 预算数量
     */
    @Query("SELECT COUNT(*) FROM budgets WHERE is_active = 1")
    int getBudgetCount();

    /**
     * 检查分类是否已有预算
     * @param categoryId 分类ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param excludeId 排除的预算ID（用于编辑时检查）
     * @return 存在返回1，不存在返回0
     */
    @Query("SELECT COUNT(*) FROM budgets WHERE category_id = :categoryId AND is_active = 1 AND " +
           "((start_date <= :startDate AND end_date >= :startDate) OR " +
           "(start_date <= :endDate AND end_date >= :endDate) OR " +
           "(start_date >= :startDate AND end_date <= :endDate)) AND id != :excludeId")
    int checkBudgetConflict(String categoryId, long startDate, long endDate, String excludeId);
}
