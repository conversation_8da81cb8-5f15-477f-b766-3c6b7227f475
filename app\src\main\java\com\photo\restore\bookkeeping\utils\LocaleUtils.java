package com.photo.restore.bookkeeping.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Build;

import java.text.DateFormat;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import com.photo.restore.bookkeeping.R;

/**
 * 语言本地化工具类
 */
public class LocaleUtils {

    private static final String PREFS_NAME = "app_settings";
    private static final String KEY_LANGUAGE = "language";
    private static final String DEFAULT_LANGUAGE = "zh-CN";

    // 缓存格式化器以提高性能
    private static final Map<String, NumberFormat> currencyFormatCache = new HashMap<>();
    private static final Map<String, DateFormat> dateFormatCache = new HashMap<>();

    /**
     * 设置应用语言
     */
    public static Context setLocale(Context context, String languageCode) {
        // 保存语言设置
        saveLanguage(context, languageCode);

        Locale locale = getLocaleFromCode(languageCode);
        Locale.setDefault(locale);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Configuration configuration = new Configuration();
            configuration.setLocale(locale);
            return context.createConfigurationContext(configuration);
        } else {
            Resources resources = context.getResources();
            Configuration configuration = resources.getConfiguration();
            configuration.locale = locale;
            resources.updateConfiguration(configuration, resources.getDisplayMetrics());
            return context;
        }
    }

    /**
     * 获取当前语言设置
     */
    public static String getCurrentLanguage(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_LANGUAGE, DEFAULT_LANGUAGE);
    }

    /**
     * 保存语言设置
     */
    public static void saveLanguage(Context context, String languageCode) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        prefs.edit().putString(KEY_LANGUAGE, languageCode).apply();
    }

    /**
     * 从语言代码获取Locale对象
     */
    public static Locale getLocaleFromCode(String languageCode) {
        switch (languageCode) {
            case "zh-CN":
                return Locale.SIMPLIFIED_CHINESE;
            case "en-US":
                return Locale.US;
            default:
                return Locale.SIMPLIFIED_CHINESE;
        }
    }

    /**
     * 获取语言显示名称
     */
    public static String getLanguageDisplayName(Context context, String languageCode) {
        switch (languageCode) {
            case "zh-CN":
                return context.getString(R.string.chinese);
            case "en-US":
                return context.getString(R.string.english);
            default:
                return context.getString(R.string.chinese);
        }
    }

    /**
     * 获取支持的语言列表
     */
    public static String[] getSupportedLanguages() {
        return new String[]{"zh-CN", "en-US"};
    }

    /**
     * 获取支持的语言显示名称列表
     */
    public static String[] getSupportedLanguageNames(Context context) {
        return new String[]{
            context.getString(R.string.chinese),
            context.getString(R.string.english)
        };
    }

    /**
     * 应用保存的语言设置
     */
    public static Context applySavedLanguage(Context context) {
        String savedLanguage = getCurrentLanguage(context);
        return setLocale(context, savedLanguage);
    }

    /**
     * 检查是否需要重启Activity来应用语言更改
     */
    public static boolean needsActivityRestart(Context context, String newLanguageCode) {
        String currentLanguage = getCurrentLanguage(context);
        return !currentLanguage.equals(newLanguageCode);
    }

    /**
     * 获取货币符号
     */
    public static String getCurrencySymbol(String currencyCode) {
        switch (currencyCode) {
            case "CNY":
                return "¥";
            case "USD":
                return "$";
            case "EUR":
                return "€";
            case "JPY":
                return "¥";
            case "GBP":
                return "£";
            default:
                return "¥";
        }
    }

    /**
     * 获取货币显示名称
     */
    public static String getCurrencyDisplayName(Context context, String currencyCode) {
        switch (currencyCode) {
            case "CNY":
                return context.getString(R.string.currency_cny);
            case "USD":
                return context.getString(R.string.currency_usd);
            case "EUR":
                return context.getString(R.string.currency_eur);
            case "JPY":
                return context.getString(R.string.currency_jpy);
            case "GBP":
                return context.getString(R.string.currency_gbp);
            default:
                return context.getString(R.string.currency_cny);
        }
    }

    /**
     * 格式化货币金额
     */
    public static String formatCurrency(Context context, double amount, String currencyCode) {
        String symbol = getCurrencySymbol(currencyCode);
        String language = getCurrentLanguage(context);
        
        if (language.equals("zh-CN")) {
            return symbol + String.format("%.2f", amount);
        } else {
            return symbol + String.format("%.2f", amount);
        }
    }

    /**
     * 获取日期格式模式
     */
    public static String getDateFormatPattern(Context context) {
        return context.getString(R.string.date_format_pattern);
    }

    /**
     * 获取时间格式模式
     */
    public static String getTimeFormatPattern(Context context) {
        return context.getString(R.string.time_format_pattern);
    }
}
