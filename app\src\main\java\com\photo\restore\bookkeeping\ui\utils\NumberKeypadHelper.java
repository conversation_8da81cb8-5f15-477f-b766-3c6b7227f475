package com.photo.restore.bookkeeping.ui.utils;

import android.view.View;
import android.widget.EditText;

import com.google.android.material.button.MaterialButton;
import com.photo.restore.bookkeeping.R;

/**
 * 数字键盘辅助类
 * 为EditText提供自定义数字键盘功能
 */
public class NumberKeypadHelper {

    private final EditText editText;
    private final View keypadView;

    public NumberKeypadHelper(EditText editText, View keypadView) {
        this.editText = editText;
        this.keypadView = keypadView;
        setupKeypad();
    }

    /**
     * 设置键盘点击事件
     */
    private void setupKeypad() {
        // 数字按钮
        setNumberButtonListener(R.id.btn_0, "0");
        setNumberButtonListener(R.id.btn_1, "1");
        setNumberButtonListener(R.id.btn_2, "2");
        setNumberButtonListener(R.id.btn_3, "3");
        setNumberButtonListener(R.id.btn_4, "4");
        setNumberButtonListener(R.id.btn_5, "5");
        setNumberButtonListener(R.id.btn_6, "6");
        setNumberButtonListener(R.id.btn_7, "7");
        setNumberButtonListener(R.id.btn_8, "8");
        setNumberButtonListener(R.id.btn_9, "9");

        // 小数点按钮
        MaterialButton btnDot = keypadView.findViewById(R.id.btn_dot);
        btnDot.setOnClickListener(v -> insertText("."));

        // 删除按钮
        MaterialButton btnDelete = keypadView.findViewById(R.id.btn_delete);
        btnDelete.setOnClickListener(v -> deleteLastCharacter());
    }

    /**
     * 设置数字按钮监听器
     */
    private void setNumberButtonListener(int buttonId, String number) {
        MaterialButton button = keypadView.findViewById(buttonId);
        button.setOnClickListener(v -> insertText(number));
    }

    /**
     * 插入文本
     */
    private void insertText(String text) {
        if (editText == null) {
            return;
        }

        int start = editText.getSelectionStart();
        int end = editText.getSelectionEnd();
        
        String currentText = editText.getText().toString();
        
        // 特殊处理小数点
        if (".".equals(text)) {
            // 如果已经有小数点，不允许再添加
            if (currentText.contains(".")) {
                return;
            }
            // 如果是空的或者光标在开头，自动添加0
            if (currentText.isEmpty() || start == 0) {
                text = "0.";
            }
        }

        // 插入文本
        String newText = currentText.substring(0, start) + text + currentText.substring(end);
        
        // 验证新文本是否有效
        if (isValidMoneyInput(newText)) {
            editText.setText(newText);
            editText.setSelection(start + text.length());
        }
    }

    /**
     * 删除最后一个字符
     */
    private void deleteLastCharacter() {
        if (editText == null) {
            return;
        }

        int start = editText.getSelectionStart();
        int end = editText.getSelectionEnd();
        String currentText = editText.getText().toString();

        if (start > 0 && start == end) {
            // 删除光标前的一个字符
            String newText = currentText.substring(0, start - 1) + currentText.substring(start);
            editText.setText(newText);
            editText.setSelection(start - 1);
        } else if (start != end) {
            // 删除选中的文本
            String newText = currentText.substring(0, start) + currentText.substring(end);
            editText.setText(newText);
            editText.setSelection(start);
        }
    }

    /**
     * 验证金额输入是否有效
     */
    private boolean isValidMoneyInput(String input) {
        if (input.isEmpty()) {
            return true;
        }

        // 检查是否只包含数字和最多一个小数点
        if (!input.matches("^\\d*\\.?\\d*$")) {
            return false;
        }

        // 检查小数点后最多两位
        int dotIndex = input.indexOf('.');
        if (dotIndex != -1) {
            String afterDot = input.substring(dotIndex + 1);
            if (afterDot.length() > 2) {
                return false;
            }
        }

        // 检查总长度（可选）
        if (input.length() > 12) { // 最多12位数字（包括小数点）
            return false;
        }

        return true;
    }

    /**
     * 显示键盘
     */
    public void showKeypad() {
        if (keypadView != null) {
            keypadView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏键盘
     */
    public void hideKeypad() {
        if (keypadView != null) {
            keypadView.setVisibility(View.GONE);
        }
    }

    /**
     * 切换键盘显示状态
     */
    public void toggleKeypad() {
        if (keypadView != null) {
            if (keypadView.getVisibility() == View.VISIBLE) {
                hideKeypad();
            } else {
                showKeypad();
            }
        }
    }
}
