package com.photo.restore.bookkeeping.ui.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.View;

import androidx.annotation.Nullable;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 自定义饼图View
 */
public class PieChartView extends View {

    private Paint paint;
    private RectF rectF;
    private List<PieSlice> slices;
    private float centerX, centerY, radius;
    
    // 默认颜色数组
    private static final String[] DEFAULT_COLORS = {
        "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
        "#DDA0DD", "#98D8C8", "#55A3FF", "#FFD93D", "#6BCF7F",
        "#A8E6CF", "#FFB6C1", "#87CEEB", "#F0E68C", "#D3D3D3"
    };

    public PieChartView(Context context) {
        super(context);
        init();
    }

    public PieChartView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public PieChartView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        rectF = new RectF();
        slices = new ArrayList<>();
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        
        centerX = w / 2f;
        centerY = h / 2f;
        radius = Math.min(w, h) / 2f * 0.8f; // 留出一些边距
        
        rectF.set(centerX - radius, centerY - radius, centerX + radius, centerY + radius);
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        
        if (slices.isEmpty()) {
            drawEmptyState(canvas);
            return;
        }
        
        float startAngle = -90f; // 从顶部开始
        
        for (PieSlice slice : slices) {
            paint.setColor(slice.color);
            canvas.drawArc(rectF, startAngle, slice.sweepAngle, true, paint);
            startAngle += slice.sweepAngle;
        }
    }

    private void drawEmptyState(Canvas canvas) {
        paint.setColor(Color.LTGRAY);
        paint.setStyle(Paint.Style.STROKE);
        paint.setStrokeWidth(4f);
        canvas.drawCircle(centerX, centerY, radius, paint);
        
        paint.setStyle(Paint.Style.FILL);
        paint.setColor(Color.GRAY);
        paint.setTextSize(48f);
        paint.setTextAlign(Paint.Align.CENTER);
        
        String emptyText = "📊";
        float textY = centerY + (paint.descent() + paint.ascent()) / 2;
        canvas.drawText(emptyText, centerX, textY, paint);
    }

    /**
     * 设置饼图数据
     */
    public void setData(List<PieSliceData> data) {
        slices.clear();
        
        if (data == null || data.isEmpty()) {
            invalidate();
            return;
        }
        
        // 计算总值
        BigDecimal total = BigDecimal.ZERO;
        for (PieSliceData item : data) {
            total = total.add(item.value);
        }
        
        if (total.compareTo(BigDecimal.ZERO) <= 0) {
            invalidate();
            return;
        }
        
        // 计算每个扇形的角度
        for (int i = 0; i < data.size(); i++) {
            PieSliceData item = data.get(i);
            float percentage = item.value.divide(total, 4, BigDecimal.ROUND_HALF_UP).floatValue();
            float sweepAngle = percentage * 360f;
            
            String colorStr = item.color != null ? item.color : DEFAULT_COLORS[i % DEFAULT_COLORS.length];
            int color = Color.parseColor(colorStr);
            
            slices.add(new PieSlice(sweepAngle, color, item.label, percentage));
        }
        
        invalidate();
    }

    /**
     * 饼图数据项
     */
    public static class PieSliceData {
        public String label;
        public BigDecimal value;
        public String color;
        
        public PieSliceData(String label, BigDecimal value, String color) {
            this.label = label;
            this.value = value;
            this.color = color;
        }
    }

    /**
     * 内部饼图扇形类
     */
    private static class PieSlice {
        float sweepAngle;
        int color;
        String label;
        float percentage;
        
        PieSlice(float sweepAngle, int color, String label, float percentage) {
            this.sweepAngle = sweepAngle;
            this.color = color;
            this.label = label;
            this.percentage = percentage;
        }
    }
}
