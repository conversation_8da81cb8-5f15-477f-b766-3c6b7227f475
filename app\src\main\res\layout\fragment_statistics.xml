<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.statistics.StatisticsFragment">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorSurface"
        app:title="@string/nav_statistics"
        app:titleTextColor="?attr/colorOnSurface" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 时间选择 -->
            <com.google.android.material.button.MaterialButtonToggleGroup
                android:id="@+id/toggle_period"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:singleSelection="true">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_month"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/filter_month" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_year"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/filter_year" />

            </com.google.android.material.button.MaterialButtonToggleGroup>

            <!-- 收支概览 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/income_expense_overview"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="horizontal">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/income"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:id="@+id/tv_total_income"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/default_amount"
                                android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                                android:textColor="@color/income_color"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/expense"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:id="@+id/tv_total_expense"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/default_amount"
                                android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                                android:textColor="@color/expense_color"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/net_income"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant" />

                            <TextView
                                android:id="@+id/tv_net_income"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:text="@string/default_amount"
                                android:textAppearance="@style/TextAppearance.Material3.TitleLarge"
                                android:textColor="?attr/colorPrimary"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 支出分布图表 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/expense_distribution"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <!-- 饼图和图例容器 -->
                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp">

                        <!-- 饼图和图例 -->
                        <LinearLayout
                            android:id="@+id/chart_container"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:visibility="visible">

                            <!-- 饼图 -->
                            <com.photo.restore.bookkeeping.ui.view.PieChartView
                                android:id="@+id/pie_chart"
                                android:layout_width="180dp"
                                android:layout_height="180dp"
                                android:layout_marginEnd="12dp" />

                            <!-- 图例 -->
                            <com.photo.restore.bookkeeping.ui.view.ChartLegendView
                                android:id="@+id/chart_legend"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_gravity="center_vertical" />

                        </LinearLayout>

                        <!-- 无数据状态 -->
                        <LinearLayout
                            android:id="@+id/empty_state_container"
                            android:layout_width="match_parent"
                            android:layout_height="180dp"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:visibility="gone">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📊"
                                android:textSize="48sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/no_expense_data"
                                android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                                android:textColor="?attr/colorOnSurfaceVariant"
                                android:gravity="center" />

                        </LinearLayout>

                    </FrameLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 分类统计 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/category_statistics"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_category_statistics"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_category_statistic" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</LinearLayout>
