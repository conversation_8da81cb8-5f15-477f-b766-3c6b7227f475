package com.photo.restore.bookkeeping.utils;

import android.util.Log;

import java.util.HashMap;
import java.util.Map;

/**
 * 性能监控工具类
 */
public class PerformanceUtils {
    
    private static final String TAG = "PerformanceUtils";
    private static final boolean DEBUG = true; // 可以通过BuildConfig.DEBUG控制
    
    // 存储计时器
    private static final Map<String, Long> timers = new HashMap<>();
    
    /**
     * 开始计时
     */
    public static void startTimer(String tag) {
        if (!DEBUG) return;
        timers.put(tag, System.currentTimeMillis());
    }
    
    /**
     * 结束计时并输出结果
     */
    public static long endTimer(String tag) {
        if (!DEBUG) return 0;
        
        Long startTime = timers.remove(tag);
        if (startTime == null) {
            Log.w(TAG, "Timer not found for tag: " + tag);
            return 0;
        }
        
        long duration = System.currentTimeMillis() - startTime;
        Log.d(TAG, String.format("%s took %d ms", tag, duration));
        return duration;
    }
    
    /**
     * 测量方法执行时间
     */
    public static void measureTime(String tag, Runnable runnable) {
        if (!DEBUG) {
            runnable.run();
            return;
        }
        
        long startTime = System.currentTimeMillis();
        runnable.run();
        long duration = System.currentTimeMillis() - startTime;
        Log.d(TAG, String.format("%s took %d ms", tag, duration));
    }
    
    /**
     * 测量数据库操作性能
     */
    public static <T> T measureDatabaseOperation(String operation, DatabaseOperation<T> dbOperation) {
        if (!DEBUG) {
            try {
                return dbOperation.execute();
            } catch (Exception e) {
                Log.e(TAG, "Database operation failed: " + operation, e);
                return null;
            }
        }
        
        long startTime = System.currentTimeMillis();
        try {
            T result = dbOperation.execute();
            long duration = System.currentTimeMillis() - startTime;
            Log.d(TAG, String.format("DB Operation [%s] took %d ms", operation, duration));
            
            // 如果操作时间过长，记录警告
            if (duration > 1000) { // 超过1秒
                Log.w(TAG, String.format("Slow DB operation detected: %s took %d ms", operation, duration));
            }
            
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            Log.e(TAG, String.format("DB Operation [%s] failed after %d ms", operation, duration), e);
            return null;
        }
    }
    
    /**
     * 记录内存使用情况
     */
    public static void logMemoryUsage(String tag) {
        if (!DEBUG) return;
        
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        Log.d(TAG, String.format("[%s] Memory: Used=%s, Free=%s, Total=%s, Max=%s", 
            tag,
            MemoryUtils.formatBytes(usedMemory),
            MemoryUtils.formatBytes(freeMemory),
            MemoryUtils.formatBytes(totalMemory),
            MemoryUtils.formatBytes(maxMemory)));
    }
    
    /**
     * 记录方法调用
     */
    public static void logMethodCall(String className, String methodName) {
        if (!DEBUG) return;
        Log.v(TAG, String.format("Method call: %s.%s()", className, methodName));
    }
    
    /**
     * 记录方法调用（带参数）
     */
    public static void logMethodCall(String className, String methodName, Object... params) {
        if (!DEBUG) return;
        
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("Method call: %s.%s(", className, methodName));
        
        for (int i = 0; i < params.length; i++) {
            if (i > 0) sb.append(", ");
            sb.append(params[i] != null ? params[i].toString() : "null");
        }
        
        sb.append(")");
        Log.v(TAG, sb.toString());
    }
    
    /**
     * 记录UI操作性能
     */
    public static void measureUIOperation(String operation, Runnable uiOperation) {
        if (!DEBUG) {
            uiOperation.run();
            return;
        }
        
        long startTime = System.currentTimeMillis();
        uiOperation.run();
        long duration = System.currentTimeMillis() - startTime;
        
        Log.d(TAG, String.format("UI Operation [%s] took %d ms", operation, duration));
        
        // 如果UI操作时间过长，记录警告
        if (duration > 16) { // 超过一帧时间（60fps = 16.67ms）
            Log.w(TAG, String.format("Slow UI operation detected: %s took %d ms", operation, duration));
        }
    }
    
    /**
     * 记录网络请求性能
     */
    public static <T> T measureNetworkOperation(String operation, NetworkOperation<T> networkOperation) {
        if (!DEBUG) {
            try {
                return networkOperation.execute();
            } catch (Exception e) {
                Log.e(TAG, "Network operation failed: " + operation, e);
                return null;
            }
        }
        
        long startTime = System.currentTimeMillis();
        try {
            T result = networkOperation.execute();
            long duration = System.currentTimeMillis() - startTime;
            Log.d(TAG, String.format("Network Operation [%s] took %d ms", operation, duration));
            
            // 如果网络操作时间过长，记录警告
            if (duration > 5000) { // 超过5秒
                Log.w(TAG, String.format("Slow network operation detected: %s took %d ms", operation, duration));
            }
            
            return result;
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            Log.e(TAG, String.format("Network Operation [%s] failed after %d ms", operation, duration), e);
            return null;
        }
    }
    
    /**
     * 清理所有计时器
     */
    public static void clearAllTimers() {
        timers.clear();
    }
    
    /**
     * 获取当前活跃的计时器数量
     */
    public static int getActiveTimerCount() {
        return timers.size();
    }
    
    /**
     * 数据库操作接口
     */
    public interface DatabaseOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 网络操作接口
     */
    public interface NetworkOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 性能统计信息
     */
    public static class PerformanceStats {
        private long totalOperations = 0;
        private long totalTime = 0;
        private long maxTime = 0;
        private long minTime = Long.MAX_VALUE;
        
        public void addOperation(long duration) {
            totalOperations++;
            totalTime += duration;
            maxTime = Math.max(maxTime, duration);
            minTime = Math.min(minTime, duration);
        }
        
        public double getAverageTime() {
            return totalOperations > 0 ? (double) totalTime / totalOperations : 0;
        }
        
        public long getTotalOperations() {
            return totalOperations;
        }
        
        public long getTotalTime() {
            return totalTime;
        }
        
        public long getMaxTime() {
            return maxTime;
        }
        
        public long getMinTime() {
            return minTime == Long.MAX_VALUE ? 0 : minTime;
        }
        
        @Override
        public String toString() {
            return String.format("PerformanceStats{operations=%d, avg=%.1fms, max=%dms, min=%dms, total=%dms}",
                totalOperations, getAverageTime(), maxTime, getMinTime(), totalTime);
        }
    }
    
    // 全局性能统计
    private static final Map<String, PerformanceStats> globalStats = new HashMap<>();
    
    /**
     * 记录操作到全局统计
     */
    public static void recordOperation(String operation, long duration) {
        if (!DEBUG) return;
        
        PerformanceStats stats = globalStats.get(operation);
        if (stats == null) {
            stats = new PerformanceStats();
            globalStats.put(operation, stats);
        }
        stats.addOperation(duration);
    }
    
    /**
     * 获取全局性能统计
     */
    public static PerformanceStats getGlobalStats(String operation) {
        return globalStats.get(operation);
    }
    
    /**
     * 打印所有全局统计信息
     */
    public static void printAllGlobalStats() {
        if (!DEBUG) return;
        
        Log.d(TAG, "=== Global Performance Statistics ===");
        for (Map.Entry<String, PerformanceStats> entry : globalStats.entrySet()) {
            Log.d(TAG, String.format("%s: %s", entry.getKey(), entry.getValue()));
        }
        Log.d(TAG, "=====================================");
    }
}
