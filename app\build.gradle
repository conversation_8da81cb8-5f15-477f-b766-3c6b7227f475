apply {
    from "${rootDir.path}/config/gradle/app.gradle"

    from "android_guard.gradle"
}

android {
    namespace Android.applicationId
}

dependencies {
    implementation fileTree(include: ['*.aar', '*.jar'], dir: 'libs')

    // AndroidX Core
    implementation Deps.core_ktx
    implementation Deps.appcompat
    implementation Deps.material
    implementation Deps.constraintlayout
    implementation Deps.recyclerview
    implementation Deps.viewpager2
    implementation Deps.swiperefreshlayout
    implementation Deps.fragment
    implementation Deps.activity

    // Navigation
    implementation Deps.navigation_fragment
    implementation Deps.navigation_ui

    // Room Database
    implementation Deps.room_runtime_android
    implementation Deps.room_common_jvm
    kapt Deps.room_compiler

    // Lifecycle
    implementation Deps.lifecycle_runtime_ktx
    implementation Deps.lifecycle_viewmodel_ktx
    implementation Deps.lifecycle_livedata_ktx

    // Security
    implementation Deps.security_crypto
    implementation Deps.biometric

    // Preferences
    implementation Deps.preference

    // Network
    implementation Deps.okhttp
    implementation Deps.okhttp_logging

    // JSON
    implementation Deps.gson

    // Testing
    testImplementation Deps.junit
    androidTestImplementation Deps.ext_junit
    androidTestImplementation Deps.espresso_core
}
