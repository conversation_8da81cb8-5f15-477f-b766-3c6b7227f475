package com.photo.restore.bookkeeping;

import android.content.Context;
import android.os.Bundle;

import androidx.appcompat.app.AppCompatActivity;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;
import androidx.navigation.fragment.NavHostFragment;
import androidx.navigation.ui.NavigationUI;

import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.photo.restore.bookkeeping.data.DataInitializer;
import com.photo.restore.bookkeeping.utils.LocaleUtils;
import com.photo.restore.bookkeeping.utils.ThemeUtils;
import com.photo.restore.bookkeeping.utils.MemoryUtils;
import com.photo.restore.bookkeeping.utils.AppStartupOptimizer;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;

/**
 * 主Activity - Money记账应用
 * 全球记账管理应用，支持多语言（中文、英文），提供直观的财务管理功能
 */
public class MainActivity extends AppCompatActivity {

    private NavController navController;
    private BottomNavigationView bottomNavigationView;
    private DataInitializer dataInitializer;

    @Override
    protected void attachBaseContext(Context newBase) {
        // 设置默认语言为英语
        Context context = LocaleUtils.setLocale(newBase, "en-US");
        super.attachBaseContext(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        // 应用保存的主题设置
        ThemeUtils.applySavedTheme(this);

        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeData();

        // 预加载数据库实例（异步）
        preloadDatabase();

        // 开始内存监控（仅在Debug模式下）
        if (BuildConfig.DEBUG) {
            MemoryUtils.startMemoryMonitoring(this);
        }

        // 初始化启动优化器
        AppStartupOptimizer.initialize(getApplication());

        // 延迟执行非关键任务
        AppStartupOptimizer.scheduleNonCriticalTasks(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (navController == null) {
            setupNavigation();
        }
    }

    /**
     * 预加载数据库实例以提高后续访问性能
     */
    private void preloadDatabase() {
        new Thread(() -> {
            try {
                BookkeepingDatabase.getInstance(this);
            } catch (Exception e) {
                // 静默处理预加载错误
            }
        }).start();
    }

    /**
     * 设置导航组件
     */
    private void setupNavigation() {
        // 使用Navigation.findNavController方法获取NavController
        navController = Navigation.findNavController(this, R.id.nav_host_fragment);
        setupNavigationUI();
    }

    /**
     * 设置导航UI
     */
    private void setupNavigationUI() {
        // 获取底部导航视图
        bottomNavigationView = findViewById(R.id.bottom_navigation);

        // 将底部导航与NavController关联
        NavigationUI.setupWithNavController(bottomNavigationView, navController);

        // 设置底部导航项选择监听器
        bottomNavigationView.setOnItemSelectedListener(item -> {
            int itemId = item.getItemId();

            if (itemId == R.id.navigation_home) {
                navController.navigate(R.id.homeFragment);
                return true;
            } else if (itemId == R.id.navigation_transactions) {
                navController.navigate(R.id.transactionsFragment);
                return true;
            } else if (itemId == R.id.navigation_add) {
                navController.navigate(R.id.addTransactionFragment);
                return true;
            } else if (itemId == R.id.navigation_statistics) {
                navController.navigate(R.id.statisticsFragment);
                return true;
            } else if (itemId == R.id.navigation_settings) {
                navController.navigate(R.id.settingsFragment);
                return true;
            }

            return false;
        });
    }

    /**
     * 初始化默认分类数据
     */
    private void initializeData() {
        dataInitializer = new DataInitializer(getApplication());
        dataInitializer.initializeDefaultCategories();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (dataInitializer != null) {
            dataInitializer.cleanup();
        }

        // 停止内存监控
        if (BuildConfig.DEBUG) {
            MemoryUtils.stopMemoryMonitoring();
        }

        // 关闭启动优化器
        AppStartupOptimizer.shutdown();

        // 关闭启动优化器
        AppStartupOptimizer.shutdown();
    }

    @Override
    public boolean onSupportNavigateUp() {
        if (navController != null) {
            return navController.navigateUp() || super.onSupportNavigateUp();
        }
        return super.onSupportNavigateUp();
    }


}