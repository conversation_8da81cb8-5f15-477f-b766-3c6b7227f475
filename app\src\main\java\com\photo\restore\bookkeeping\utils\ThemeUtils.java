package com.photo.restore.bookkeeping.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import androidx.appcompat.app.AppCompatDelegate;

import com.photo.restore.bookkeeping.R;

/**
 * 主题管理工具类
 * 负责深色模式的切换和主题相关的工具方法
 */
public class ThemeUtils {
    
    private static final String PREFS_NAME = "app_settings";
    private static final String KEY_DARK_MODE = "dark_mode";
    private static final String KEY_FOLLOW_SYSTEM = "follow_system_theme";
    
    // 主题模式常量
    public static final int THEME_LIGHT = 0;
    public static final int THEME_DARK = 1;
    public static final int THEME_FOLLOW_SYSTEM = 2;
    
    /**
     * 应用保存的主题设置
     */
    public static void applySavedTheme(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean followSystem = prefs.getBoolean(KEY_FOLLOW_SYSTEM, true);
        
        if (followSystem) {
            // 跟随系统主题
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
        } else {
            // 使用用户设置的主题
            boolean darkMode = prefs.getBoolean(KEY_DARK_MODE, false);
            AppCompatDelegate.setDefaultNightMode(
                darkMode ? AppCompatDelegate.MODE_NIGHT_YES : AppCompatDelegate.MODE_NIGHT_NO
            );
        }
    }
    
    /**
     * 设置主题模式
     */
    public static void setThemeMode(Context context, int themeMode) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        switch (themeMode) {
            case THEME_LIGHT:
                editor.putBoolean(KEY_FOLLOW_SYSTEM, false);
                editor.putBoolean(KEY_DARK_MODE, false);
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
                break;
                
            case THEME_DARK:
                editor.putBoolean(KEY_FOLLOW_SYSTEM, false);
                editor.putBoolean(KEY_DARK_MODE, true);
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
                break;
                
            case THEME_FOLLOW_SYSTEM:
                editor.putBoolean(KEY_FOLLOW_SYSTEM, true);
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_FOLLOW_SYSTEM);
                break;
        }
        
        editor.apply();
    }
    
    /**
     * 获取当前主题模式
     */
    public static int getCurrentThemeMode(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean followSystem = prefs.getBoolean(KEY_FOLLOW_SYSTEM, true);
        
        if (followSystem) {
            return THEME_FOLLOW_SYSTEM;
        } else {
            boolean darkMode = prefs.getBoolean(KEY_DARK_MODE, false);
            return darkMode ? THEME_DARK : THEME_LIGHT;
        }
    }
    
    /**
     * 检查当前是否为深色模式
     */
    public static boolean isDarkMode(Context context) {
        int nightModeFlags = context.getResources().getConfiguration().uiMode & Configuration.UI_MODE_NIGHT_MASK;
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES;
    }
    
    /**
     * 获取主题模式显示名称
     */
    public static String getThemeModeDisplayName(Context context, int themeMode) {
        switch (themeMode) {
            case THEME_LIGHT:
                return context.getString(R.string.theme_light);
            case THEME_DARK:
                return context.getString(R.string.theme_dark);
            case THEME_FOLLOW_SYSTEM:
                return context.getString(R.string.theme_follow_system);
            default:
                return context.getString(R.string.theme_follow_system);
        }
    }
    
    /**
     * 获取所有支持的主题模式
     */
    public static int[] getSupportedThemeModes() {
        return new int[]{THEME_LIGHT, THEME_DARK, THEME_FOLLOW_SYSTEM};
    }
    
    /**
     * 获取所有主题模式的显示名称
     */
    public static String[] getThemeModeDisplayNames(Context context) {
        int[] modes = getSupportedThemeModes();
        String[] names = new String[modes.length];
        for (int i = 0; i < modes.length; i++) {
            names[i] = getThemeModeDisplayName(context, modes[i]);
        }
        return names;
    }
    
    /**
     * 检查是否需要重启Activity来应用主题变化
     */
    public static boolean needsActivityRestart(Context context, int newThemeMode) {
        int currentMode = getCurrentThemeMode(context);
        return currentMode != newThemeMode;
    }
    
    /**
     * 获取适合当前主题的状态栏颜色
     */
    public static int getStatusBarColor(Context context) {
        if (isDarkMode(context)) {
            return context.getColor(R.color.status_bar);
        } else {
            return context.getColor(R.color.status_bar);
        }
    }
    
    /**
     * 获取适合当前主题的导航栏颜色
     */
    public static int getNavigationBarColor(Context context) {
        if (isDarkMode(context)) {
            return context.getColor(R.color.navigation_bar);
        } else {
            return context.getColor(R.color.navigation_bar);
        }
    }
}
