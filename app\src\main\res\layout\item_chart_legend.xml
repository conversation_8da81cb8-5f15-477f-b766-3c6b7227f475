<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingHorizontal="4dp"
    android:paddingVertical="4dp"
    android:gravity="center_vertical">

    <!-- 颜色指示器 -->
    <View
        android:id="@+id/color_indicator"
        android:layout_width="12dp"
        android:layout_height="12dp"
        android:layout_marginEnd="8dp"
        android:background="@drawable/circle_background" />

    <!-- 分类名称 -->
    <TextView
        android:id="@+id/tv_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Category Name"
        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
        android:textColor="?attr/colorOnSurface"
        android:maxLines="1"
        android:ellipsize="end" />

    <!-- 百分比 -->
    <TextView
        android:id="@+id/tv_percentage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:minWidth="50dp"
        android:text="25.0%"
        android:textAppearance="@style/TextAppearance.Material3.BodySmall"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:gravity="end"
        android:maxLines="1" />

</LinearLayout>
