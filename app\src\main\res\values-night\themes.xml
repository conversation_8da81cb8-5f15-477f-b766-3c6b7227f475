<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme (Dark Mode) -->
    <style name="Base.Theme.BookKeeping" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary Colors -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/on_primary</item>

        <!-- Secondary Colors -->
        <item name="colorSecondary">@color/secondary</item>
        <item name="colorSecondaryVariant">@color/secondary_variant</item>
        <item name="colorOnSecondary">@color/on_secondary</item>

        <!-- Background Colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorSurfaceVariant">@color/surface_variant</item>
        <item name="colorOnBackground">@color/on_background</item>
        <item name="colorOnSurface">@color/on_surface</item>
        <item name="colorOnSurfaceVariant">@color/on_surface_variant</item>

        <!-- Error Colors -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/on_error</item>

        <!-- Status Bar -->
        <item name="android:statusBarColor">?attr/colorPrimary</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation Bar -->
        <item name="android:navigationBarColor">@color/navigation_bar</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Window Background -->
        <item name="android:windowBackground">@color/background</item>
    </style>

    <!-- Custom Styles for Dark Mode -->
    <style name="CardViewStyle" parent="Widget.Material3.CardView.Elevated">
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
    </style>

    <style name="TextAppearance.App.Headline" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.App.Body" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="TextAppearance.App.Caption" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_secondary</item>
    </style>
</resources>