package com.photo.restore.bookkeeping.ui.settings;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import com.photo.restore.bookkeeping.utils.LocaleUtils;
import com.photo.restore.bookkeeping.utils.ThemeUtils;

/**
 * 设置ViewModel
 * 管理应用设置相关的数据和业务逻辑
 */
public class SettingsViewModel extends AndroidViewModel {

    private static final String PREFS_NAME = "app_settings";
    private static final String KEY_CURRENCY = "currency";

    private final SharedPreferences sharedPreferences;

    // 设置数据
    private final MutableLiveData<String> currency = new MutableLiveData<>();
    private final MutableLiveData<Integer> themeMode = new MutableLiveData<>();
    private final MutableLiveData<String> versionName = new MutableLiveData<>();

    public SettingsViewModel(@NonNull Application application) {
        super(application);
        
        // 初始化SharedPreferences
        sharedPreferences = application.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        // 加载设置
        loadSettings();
        
        // 加载版本信息
        loadVersionInfo();
    }

    /**
     * 加载设置
     */
    private void loadSettings() {
        // 加载货币设置，默认为美元
        String curr = sharedPreferences.getString(KEY_CURRENCY, "USD");
        currency.setValue(curr);

        // 加载主题模式设置，默认跟随系统
        int theme = ThemeUtils.getCurrentThemeMode(getApplication());
        themeMode.setValue(theme);
    }

    /**
     * 加载版本信息
     */
    private void loadVersionInfo() {
        try {
            PackageManager packageManager = getApplication().getPackageManager();
            PackageInfo packageInfo = packageManager.getPackageInfo(getApplication().getPackageName(), 0);
            versionName.setValue(packageInfo.versionName);
        } catch (PackageManager.NameNotFoundException e) {
            versionName.setValue("1.0.0");
        }
    }



    /**
     * 获取货币设置
     */
    public LiveData<String> getCurrency() {
        return currency;
    }

    /**
     * 设置货币
     */
    public void setCurrency(String curr) {
        currency.setValue(curr);
        sharedPreferences.edit().putString(KEY_CURRENCY, curr).apply();
    }

    /**
     * 获取主题模式设置
     */
    public LiveData<Integer> getThemeMode() {
        return themeMode;
    }

    /**
     * 设置主题模式
     */
    public void setThemeMode(int mode) {
        themeMode.setValue(mode);
        ThemeUtils.setThemeMode(getApplication(), mode);
    }

    /**
     * 获取版本名称
     */
    public LiveData<String> getVersionName() {
        return versionName;
    }



    /**
     * 重置所有设置
     */
    public void resetSettings() {
        sharedPreferences.edit().clear().apply();
        loadSettings();
    }



    /**
     * 获取当前货币代码
     */
    public String getCurrentCurrency() {
        return currency.getValue() != null ? currency.getValue() : "CNY";
    }

    /**
     * 获取当前深色模式状态
     */
    public boolean getCurrentDarkMode() {
        Integer mode = themeMode.getValue();
        return mode != null && mode == AppCompatDelegate.MODE_NIGHT_YES;
    }
}
