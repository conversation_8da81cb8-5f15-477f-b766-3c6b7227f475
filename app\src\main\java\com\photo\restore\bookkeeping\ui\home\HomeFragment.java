package com.photo.restore.bookkeeping.ui.home;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.ui.adapter.TransactionWithDetailsAdapter;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 首页Fragment
 * 显示总资产、收支概览、快速操作和最近交易
 */
public class HomeFragment extends Fragment {

    private HomeViewModel homeViewModel;
    
    // UI组件
    private MaterialToolbar toolbar;
    private TextView tvTotalAssets;
    private TextView tvMonthlyIncome;
    private TextView tvMonthlyExpense;
    private TextView tvNoTransactions;
    private MaterialButton btnAddIncome;
    private MaterialButton btnAddExpense;
    private RecyclerView rvRecentTransactions;
    
    // 适配器
    private TransactionWithDetailsAdapter transactionAdapter;
    
    // 格式化器
    private NumberFormat currencyFormat;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化ViewModel
        homeViewModel = new ViewModelProvider(this).get(HomeViewModel.class);
        
        // 初始化货币格式化器
        currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
        currencyFormat.setCurrency(Currency.getInstance("CNY"));
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_home, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupRecyclerView();
        setupClickListeners();
        observeData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        tvTotalAssets = view.findViewById(R.id.tv_total_assets);
        tvMonthlyIncome = view.findViewById(R.id.tv_monthly_income);
        tvMonthlyExpense = view.findViewById(R.id.tv_monthly_expense);
        tvNoTransactions = view.findViewById(R.id.tv_no_transactions);
        btnAddIncome = view.findViewById(R.id.btn_add_income);
        btnAddExpense = view.findViewById(R.id.btn_add_expense);
        rvRecentTransactions = view.findViewById(R.id.rv_recent_transactions);
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        transactionAdapter = new TransactionWithDetailsAdapter(new ArrayList<>(), transactionWithDetails -> {
            // 点击交易项目，可以导航到交易详情页面
            // TODO: 实现交易详情页面导航
        });

        rvRecentTransactions.setLayoutManager(new LinearLayoutManager(getContext()));
        rvRecentTransactions.setAdapter(transactionAdapter);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 添加收入按钮
        btnAddIncome.setOnClickListener(v -> {
            // 导航到添加交易页面，并设置为收入模式
            Bundle bundle = new Bundle();
            bundle.putString("transaction_type", "INCOME");
            Navigation.findNavController(v).navigate(R.id.addTransactionFragment, bundle);
        });

        // 添加支出按钮
        btnAddExpense.setOnClickListener(v -> {
            // 导航到添加交易页面，并设置为支出模式
            Bundle bundle = new Bundle();
            bundle.putString("transaction_type", "EXPENSE");
            Navigation.findNavController(v).navigate(R.id.addTransactionFragment, bundle);
        });
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        // 观察总资产
        homeViewModel.getTotalAssets().observe(getViewLifecycleOwner(), totalAssets -> {
            if (totalAssets != null) {
                tvTotalAssets.setText(formatCurrency(totalAssets));
            } else {
                tvTotalAssets.setText(formatCurrency(BigDecimal.ZERO));
            }
        });

        // 观察本月收入
        homeViewModel.getMonthlyIncome().observe(getViewLifecycleOwner(), monthlyIncome -> {
            if (monthlyIncome != null) {
                tvMonthlyIncome.setText(formatCurrency(monthlyIncome));
            } else {
                tvMonthlyIncome.setText(formatCurrency(BigDecimal.ZERO));
            }
        });

        // 观察本月支出
        homeViewModel.getMonthlyExpense().observe(getViewLifecycleOwner(), monthlyExpense -> {
            if (monthlyExpense != null) {
                tvMonthlyExpense.setText(formatCurrency(monthlyExpense));
            } else {
                tvMonthlyExpense.setText(formatCurrency(BigDecimal.ZERO));
            }
        });

        // 观察最近交易
        homeViewModel.getRecentTransactions().observe(getViewLifecycleOwner(), transactions -> {
            if (transactions != null && !transactions.isEmpty()) {
                transactionAdapter.updateTransactions(transactions);
                rvRecentTransactions.setVisibility(View.VISIBLE);
                tvNoTransactions.setVisibility(View.GONE);
            } else {
                rvRecentTransactions.setVisibility(View.GONE);
                tvNoTransactions.setVisibility(View.VISIBLE);
            }
        });
    }

    /**
     * 格式化货币显示
     */
    private String formatCurrency(BigDecimal amount) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        return currencyFormat.format(amount);
    }

    @Override
    public void onResume() {
        super.onResume();
        // 刷新数据
        homeViewModel.refreshData();
    }
}
