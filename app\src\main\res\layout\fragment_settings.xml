<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.settings.SettingsFragment">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:title="@string/nav_settings"
        app:titleTextColor="?attr/colorOnPrimary" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 基本设置 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="@string/basic_settings"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />



                    <!-- 货币设置 -->
                    <LinearLayout
                        android:id="@+id/layout_currency"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/currency"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:id="@+id/tv_currency"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/currency_code_usd"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <!-- 主题设置 -->
                    <LinearLayout
                        android:id="@+id/layout_theme"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/theme"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:id="@+id/tv_theme"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/theme_follow_system"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 账户和分类管理 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="@string/account_category_management"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <!-- 账户管理 -->
                    <LinearLayout
                        android:id="@+id/layout_account_management"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/account_management"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/arrow_right"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                    <!-- 分类管理 -->
                    <LinearLayout
                        android:id="@+id/layout_category_management"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/category_management"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/arrow_right"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 数据管理 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="@string/data_management"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <!-- 备份 -->
                    <LinearLayout
                        android:id="@+id/layout_backup"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/backup"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                    <!-- 导出 -->
                    <LinearLayout
                        android:id="@+id/layout_export"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/export"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 关于 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="@string/about_app"
                        android:textAppearance="@style/TextAppearance.Material3.TitleMedium"
                        android:textColor="?attr/colorOnSurface" />

                    <!-- 关于 -->
                    <LinearLayout
                        android:id="@+id/layout_about"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="?attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:padding="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@string/about"
                            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
                            android:textColor="?attr/colorOnSurface" />

                        <TextView
                            android:id="@+id/tv_version"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/default_version"
                            android:textAppearance="@style/TextAppearance.Material3.BodyMedium"
                            android:textColor="?attr/colorOnSurfaceVariant" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
