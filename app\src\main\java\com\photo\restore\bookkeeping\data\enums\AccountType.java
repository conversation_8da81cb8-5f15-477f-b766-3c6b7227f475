package com.photo.restore.bookkeeping.data.enums;

import android.content.Context;
import com.photo.restore.bookkeeping.R;

/**
 * 账户类型枚举
 */
public enum AccountType {
    /**
     * 现金
     */
    CASH(R.string.account_type_cash, "fas fa-money-bill-wave"),

    /**
     * 储蓄卡
     */
    SAVINGS_CARD(R.string.account_type_savings_card, "fas fa-credit-card"),

    /**
     * 信用卡
     */
    CREDIT_CARD(R.string.account_type_credit_card, "fas fa-credit-card"),

    /**
     * 支付宝
     */
    ALIPAY(R.string.account_type_alipay, "fab fa-alipay"),

    /**
     * 微信
     */
    WECHAT(R.string.account_type_wechat, "fab fa-weixin"),

    /**
     * 其他电子钱包
     */
    E_WALLET(R.string.account_type_e_wallet, "fas fa-wallet"),

    /**
     * 投资账户
     */
    INVESTMENT(R.string.account_type_investment, "fas fa-chart-line"),

    /**
     * 其他
     */
    OTHER(R.string.account_type_other, "fas fa-ellipsis-h");

    private final int stringResourceId;
    private final String iconClass;

    AccountType(int stringResourceId, String iconClass) {
        this.stringResourceId = stringResourceId;
        this.iconClass = iconClass;
    }

    public String getIconClass() {
        return iconClass;
    }

    /**
     * 获取显示名称
     * @param context 上下文
     * @return 显示名称
     */
    public String getDisplayName(Context context) {
        return context.getString(stringResourceId);
    }

    /**
     * 根据语言获取显示名称（保持向后兼容）
     * @param isEnglish 是否为英文
     * @return 显示名称
     * @deprecated 使用 getDisplayName(Context context) 替代
     */
    @Deprecated
    public String getDisplayName(boolean isEnglish) {
        // 为了向后兼容，保留此方法，但建议使用新方法
        switch (this) {
            case CASH:
                return isEnglish ? "Cash" : "现金";
            case SAVINGS_CARD:
                return isEnglish ? "Savings Card" : "储蓄卡";
            case CREDIT_CARD:
                return isEnglish ? "Credit Card" : "信用卡";
            case ALIPAY:
                return isEnglish ? "Alipay" : "支付宝";
            case WECHAT:
                return isEnglish ? "WeChat" : "微信";
            case E_WALLET:
                return isEnglish ? "E-Wallet" : "电子钱包";
            case INVESTMENT:
                return isEnglish ? "Investment" : "投资账户";
            case OTHER:
                return isEnglish ? "Other" : "其他";
            default:
                return isEnglish ? "Unknown" : "未知";
        }
    }
}
