<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?attr/colorSurface"
    android:elevation="8dp"
    android:padding="8dp">

    <!-- 第一行：1 2 3 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_1"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="1"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_2"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:text="2"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_3"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="3"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

    </LinearLayout>

    <!-- 第二行：4 5 6 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_4"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="4"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_5"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:text="5"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_6"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="6"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

    </LinearLayout>

    <!-- 第三行：7 8 9 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="4dp">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_7"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="7"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_8"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:text="8"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_9"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="9"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

    </LinearLayout>

    <!-- 第四行：. 0 删除 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_dot"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="."
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_0"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="4dp"
            android:text="0"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_delete"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="56dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="⌫"
            android:textSize="18sp"
            app:cornerRadius="8dp" />

    </LinearLayout>

</LinearLayout>
