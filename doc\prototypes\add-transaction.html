<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 记账</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5B4BCF',
                        'secondary': '#A29BFE',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E84393',
                        'surface': '#FFFFFF',
                        'surface-alt': '#F8F9FA',
                        'neutral-50': '#FAFBFC',
                        'neutral-100': '#F4F5F7',
                        'neutral-200': '#E3E5E8',
                        'neutral-300': '#CFD3D8',
                        'neutral-400': '#A6ACB5',
                        'neutral-500': '#7B8794',
                        'neutral-600': '#5A6575',
                        'neutral-700': '#3E4651',
                        'neutral-800': '#2D3843',
                        'neutral-900': '#1F2937'
                    },
                    borderRadius: {
                        '2xl': '1rem',
                        '3xl': '1.5rem',
                        '4xl': '2rem'
                    },
                    boxShadow: {
                        'soft': '0 2px 12px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 24px rgba(0, 0, 0, 0.06)',
                        'large': '0 8px 40px rgba(0, 0, 0, 0.08)',
                        'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 16px rgba(0, 0, 0, 0.04)',
                        'button': '0 2px 8px rgba(108, 92, 231, 0.2)',
                        'floating': '0 8px 32px rgba(108, 92, 231, 0.15)',
                        'inner': 'inset 0 2px 4px rgba(0, 0, 0, 0.06)'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(145deg, #F8F9FA 0%, #E9ECEF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        .glass-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .amount-display {
            font-feature-settings: 'tnum';
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .number-key {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #FFFFFF 0%, #F8F9FA 100%);
            border: 1px solid rgba(108, 92, 231, 0.1);
        }
        
        .number-key:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 92, 231, 0.15);
            border-color: rgba(108, 92, 231, 0.2);
        }
        
        .number-key:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(108, 92, 231, 0.1);
        }
        
        .category-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #FFFFFF 0%, #FAFBFC 100%);
            border: 2px solid transparent;
        }
        
        .category-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-color: rgba(108, 92, 231, 0.2);
        }
        
        .category-item.selected {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            border-color: #6C5CE7;
            color: white;
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
        }
        
        .category-item.selected .category-icon {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }
        
        .category-item.selected p {
            color: white;
        }
        
        .icon-gradient {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
        }
        
        .toggle-button {
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .toggle-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }
        
        .toggle-button:active::before {
            width: 100%;
            height: 100%;
        }
        
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-8px) rotate(1deg); }
            66% { transform: translateY(4px) rotate(-1deg); }
        }
        
        .account-card {
            background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(162, 155, 254, 0.05) 100%);
            border: 1px solid rgba(108, 92, 231, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .account-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(108, 92, 231, 0.15);
            border-color: rgba(108, 92, 231, 0.3);
        }
        
        .ripple {
            position: relative;
            overflow: hidden;
        }
        
        .ripple::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(108, 92, 231, 0.2);
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
        }
        
        .ripple:active::after {
            width: 200%;
            height: 200%;
        }
        
        .input-field {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            border-color: #6C5CE7;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-neutral-50 to-neutral-100 min-h-screen flex items-center justify-center font-body">
    <div class="phone-container">
        
        <!-- 状态栏 -->
        <div class="h-11 bg-transparent flex items-center justify-between px-6 text-neutral-800 text-sm font-medium">
            <span class="font-semibold">9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <div class="flex items-center">
                    <i class="fas fa-battery-three-quarters text-xs"></i>
                    <span class="text-xs ml-1">87</span>
                </div>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="h-16 glass-card flex items-center justify-between px-6 border-b-0">
            <button class="ripple text-primary font-semibold text-base px-4 py-2 rounded-xl hover:bg-primary hover:bg-opacity-10 transition-all">
                取消
            </button>
            <h1 class="font-bold text-xl text-neutral-800">记一笔</h1>
            <button class="ripple bg-primary text-white font-semibold text-base px-4 py-2 rounded-xl shadow-button hover:bg-primary-dark transition-all">
                完成
            </button>
        </div>
        
        <!-- 金额输入区域 -->
        <div class="glass-card text-center py-12 mb-2 relative overflow-hidden">
            <!-- 装饰元素 -->
            <div class="floating-element absolute top-4 right-8 w-16 h-16 border border-primary border-opacity-10 rounded-full"></div>
            <div class="floating-element absolute bottom-6 left-6 w-8 h-8 bg-secondary bg-opacity-20 rounded-full"></div>
            
            <div class="relative z-10">
                <div class="amount-display text-7xl font-bold mb-4" id="amountDisplay">¥ 0</div>
                <p class="text-neutral-500 text-base font-medium">输入金额</p>
                <div class="flex items-center justify-center mt-3">
                    <div class="w-2 h-2 bg-primary rounded-full mr-2"></div>
                    <span class="text-xs text-neutral-400">点击数字输入</span>
                </div>
            </div>
        </div>
        
        <!-- 收支切换 -->
        <div class="px-6 py-4">
            <div class="flex bg-neutral-100 rounded-2xl p-2 shadow-inner">
                <button class="toggle-button flex-1 py-4 bg-danger text-white rounded-xl font-semibold transition-all shadow-button" id="expenseBtn">
                    <i class="fas fa-minus mr-2"></i>支出
                </button>
                <button class="toggle-button flex-1 py-4 text-neutral-600 rounded-xl font-semibold transition-all" id="incomeBtn">
                    <i class="fas fa-plus mr-2"></i>收入
                </button>
            </div>
        </div>
        
        <!-- 分类选择 -->
        <div class="px-6 py-4">
            <h3 class="font-bold text-lg text-neutral-800 mb-6">选择分类</h3>
            <div class="grid grid-cols-4 gap-4" id="categoryGrid">
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="food">
                    <div class="category-icon w-14 h-14 icon-gradient from-warning to-orange-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-utensils text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">餐饮</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="transport">
                    <div class="category-icon w-14 h-14 icon-gradient from-primary to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-bus text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">交通</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="entertainment">
                    <div class="category-icon w-14 h-14 icon-gradient from-secondary to-purple-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-gamepad text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">娱乐</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="shopping">
                    <div class="category-icon w-14 h-14 icon-gradient from-success to-emerald-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-shopping-bag text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">购物</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="healthcare">
                    <div class="category-icon w-14 h-14 icon-gradient from-red-500 to-pink-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-heartbeat text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">医疗</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="education">
                    <div class="category-icon w-14 h-14 icon-gradient from-blue-500 to-indigo-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">教育</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="housing">
                    <div class="category-icon w-14 h-14 icon-gradient from-gray-500 to-gray-400 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-home text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">住房</p>
                </div>
                <div class="category-item text-center p-4 rounded-2xl cursor-pointer shadow-card" data-category="other">
                    <div class="category-icon w-14 h-14 icon-gradient from-neutral-400 to-neutral-300 rounded-2xl flex items-center justify-center mx-auto mb-3 shadow-soft">
                        <i class="fas fa-ellipsis-h text-white text-lg"></i>
                    </div>
                    <p class="text-sm font-semibold text-neutral-700">其他</p>
                </div>
            </div>
        </div>
        
        <!-- 账户选择 -->
        <div class="px-6 py-4">
            <h3 class="font-bold text-lg text-neutral-800 mb-4">支付账户</h3>
            <div class="account-card flex items-center justify-between p-4 rounded-2xl cursor-pointer">
                <div class="flex items-center">
                    <div class="w-12 h-12 icon-gradient from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mr-4 shadow-soft">
                        <i class="fas fa-credit-card text-white"></i>
                    </div>
                    <div>
                        <p class="font-semibold text-neutral-800">招商银行</p>
                        <p class="text-sm text-neutral-500">余额 ¥8,240.50</p>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-neutral-400"></i>
            </div>
        </div>
        
        <!-- 备注输入 -->
        <div class="px-6 py-4">
            <input type="text" placeholder="添加备注（可选）" class="input-field w-full p-4 rounded-2xl text-neutral-800 placeholder-neutral-400 outline-none">
        </div>
        
        <!-- 数字键盘 -->
        <div class="absolute bottom-0 left-0 right-0 glass-card p-6 border-t border-white border-opacity-20">
            <div class="grid grid-cols-3 gap-3">
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="1">1</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="2">2</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="3">3</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="4">4</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="5">5</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="6">6</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="7">7</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="8">8</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="9">9</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number=".">.</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-700 shadow-soft" data-number="0">0</button>
                <button class="number-key h-16 rounded-2xl font-bold text-xl text-neutral-400 shadow-soft" id="deleteBtn">
                    <i class="fas fa-backspace"></i>
                </button>
            </div>
        </div>
        
    </div>

    <script>
        let currentAmount = '0';
        let isExpense = true;
        let selectedCategory = null;
        
        function updateAmountDisplay() {
            const display = document.getElementById('amountDisplay');
            const formatted = currentAmount === '0' ? '0' : currentAmount;
            display.textContent = `¥ ${formatted}`;
        }
        
        document.querySelectorAll('.number-key').forEach(btn => {
            btn.addEventListener('click', function() {
                const number = this.dataset.number;
                
                if (number) {
                    if (currentAmount === '0' && number !== '.') {
                        currentAmount = number;
                    } else if (number === '.' && currentAmount.includes('.')) {
                        return;
                    } else {
                        currentAmount += number;
                    }
                }
                updateAmountDisplay();
            });
        });
        
        document.getElementById('deleteBtn').addEventListener('click', function() {
            if (currentAmount.length > 1) {
                currentAmount = currentAmount.slice(0, -1);
            } else {
                currentAmount = '0';
            }
            updateAmountDisplay();
        });
        
        document.getElementById('expenseBtn').addEventListener('click', function() {
            isExpense = true;
            this.classList.add('bg-danger', 'text-white', 'shadow-button');
            this.classList.remove('text-neutral-600');
            document.getElementById('incomeBtn').classList.remove('bg-success', 'text-white', 'shadow-button');
            document.getElementById('incomeBtn').classList.add('text-neutral-600');
        });
        
        document.getElementById('incomeBtn').addEventListener('click', function() {
            isExpense = false;
            this.classList.add('bg-success', 'text-white', 'shadow-button');
            this.classList.remove('text-neutral-600');
            document.getElementById('expenseBtn').classList.remove('bg-danger', 'text-white', 'shadow-button');
            document.getElementById('expenseBtn').classList.add('text-neutral-600');
        });
        
        document.querySelectorAll('.category-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.category-item').forEach(i => i.classList.remove('selected'));
                this.classList.add('selected');
                selectedCategory = this.dataset.category;
            });
        });
        
        updateAmountDisplay();
    </script>
</body>
</html> 