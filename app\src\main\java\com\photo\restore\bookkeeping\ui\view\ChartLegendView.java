package com.photo.restore.bookkeeping.ui.view;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.photo.restore.bookkeeping.R;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Currency;
import java.util.List;
import java.util.Locale;

/**
 * 图表图例View
 */
public class ChartLegendView extends LinearLayout {

    private NumberFormat currencyFormat;

    public ChartLegendView(Context context) {
        super(context);
        init();
    }

    public ChartLegendView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ChartLegendView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setOrientation(VERTICAL);
        
        // 初始化货币格式化器
        currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
        currencyFormat.setCurrency(Currency.getInstance("USD"));
    }

    /**
     * 设置图例数据
     */
    public void setLegendData(List<PieChartView.PieSliceData> data) {
        removeAllViews();
        
        if (data == null || data.isEmpty()) {
            return;
        }
        
        // 计算总值用于百分比计算
        BigDecimal total = BigDecimal.ZERO;
        for (PieChartView.PieSliceData item : data) {
            total = total.add(item.value);
        }
        
        LayoutInflater inflater = LayoutInflater.from(getContext());
        
        for (int i = 0; i < data.size(); i++) {
            PieChartView.PieSliceData item = data.get(i);
            
            // 创建图例项
            View legendItem = inflater.inflate(R.layout.item_chart_legend, this, false);
            
            View colorIndicator = legendItem.findViewById(R.id.color_indicator);
            TextView tvLabel = legendItem.findViewById(R.id.tv_label);
            TextView tvPercentage = legendItem.findViewById(R.id.tv_percentage);
            
            // 设置颜色指示器
            String colorStr = item.color != null ? item.color : getDefaultColor(i);
            try {
                int color = Color.parseColor(colorStr);
                GradientDrawable drawable = new GradientDrawable();
                drawable.setShape(GradientDrawable.RECTANGLE);
                drawable.setColor(color);
                drawable.setCornerRadius(8f);
                colorIndicator.setBackground(drawable);
            } catch (IllegalArgumentException e) {
                // 如果颜色格式不正确，使用默认颜色
                colorIndicator.setBackgroundColor(Color.GRAY);
            }
            
            // 设置标签
            tvLabel.setText(item.label);

            // 计算并设置百分比
            if (total.compareTo(BigDecimal.ZERO) > 0) {
                float percentage = item.value.divide(total, 4, BigDecimal.ROUND_HALF_UP)
                                           .multiply(new BigDecimal("100"))
                                           .floatValue();
                tvPercentage.setText(String.format(Locale.US, "%.1f%%", percentage));
            } else {
                tvPercentage.setText("0.0%");
            }
            
            addView(legendItem);
        }
    }

    private String getDefaultColor(int index) {
        String[] colors = {
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7",
            "#DDA0DD", "#98D8C8", "#55A3FF", "#FFD93D", "#6BCF7F",
            "#A8E6CF", "#FFB6C1", "#87CEEB", "#F0E68C", "#D3D3D3"
        };
        return colors[index % colors.length];
    }
}
