package com.photo.restore.bookkeeping.data.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.photo.restore.bookkeeping.data.entity.Transfer;

import java.util.List;

/**
 * 转账记录数据访问对象
 */
@Dao
public interface TransferDao {

    /**
     * 获取所有转账记录（LiveData）
     * @return 转账记录列表LiveData
     */
    @Query("SELECT * FROM transfers ORDER BY date DESC, created_at DESC")
    LiveData<List<Transfer>> getAllTransfersLiveData();

    /**
     * 获取所有转账记录（同步）
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers ORDER BY date DESC, created_at DESC")
    List<Transfer> getAllTransfers();

    /**
     * 根据账户获取转账记录（作为转出账户）
     * @param accountId 账户ID
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers WHERE from_account_id = :accountId ORDER BY date DESC, created_at DESC")
    List<Transfer> getTransfersFromAccount(String accountId);

    /**
     * 根据账户获取转账记录（作为转入账户）
     * @param accountId 账户ID
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers WHERE to_account_id = :accountId ORDER BY date DESC, created_at DESC")
    List<Transfer> getTransfersToAccount(String accountId);

    /**
     * 根据账户获取所有相关转账记录
     * @param accountId 账户ID
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers WHERE from_account_id = :accountId OR to_account_id = :accountId ORDER BY date DESC, created_at DESC")
    List<Transfer> getTransfersByAccount(String accountId);

    /**
     * 根据账户获取所有相关转账记录（LiveData）
     * @param accountId 账户ID
     * @return 转账记录列表LiveData
     */
    @Query("SELECT * FROM transfers WHERE from_account_id = :accountId OR to_account_id = :accountId ORDER BY date DESC, created_at DESC")
    LiveData<List<Transfer>> getTransfersByAccountLiveData(String accountId);

    /**
     * 根据日期范围获取转账记录
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, created_at DESC")
    List<Transfer> getTransfersByDateRange(long startDate, long endDate);

    /**
     * 根据日期范围获取转账记录（LiveData）
     * @param startDate 开始日期（时间戳）
     * @param endDate 结束日期（时间戳）
     * @return 转账记录列表LiveData
     */
    @Query("SELECT * FROM transfers WHERE date >= :startDate AND date <= :endDate ORDER BY date DESC, created_at DESC")
    LiveData<List<Transfer>> getTransfersByDateRangeLiveData(long startDate, long endDate);

    /**
     * 根据ID获取转账记录
     * @param transferId 转账ID
     * @return 转账记录
     */
    @Query("SELECT * FROM transfers WHERE id = :transferId")
    Transfer getTransferById(String transferId);

    /**
     * 根据ID获取转账记录（LiveData）
     * @param transferId 转账ID
     * @return 转账记录LiveData
     */
    @Query("SELECT * FROM transfers WHERE id = :transferId")
    LiveData<Transfer> getTransferByIdLiveData(String transferId);

    /**
     * 获取最近的转账记录
     * @param limit 限制数量
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers ORDER BY date DESC, created_at DESC LIMIT :limit")
    List<Transfer> getRecentTransfers(int limit);

    /**
     * 获取最近的转账记录（LiveData）
     * @param limit 限制数量
     * @return 转账记录列表LiveData
     */
    @Query("SELECT * FROM transfers ORDER BY date DESC, created_at DESC LIMIT :limit")
    LiveData<List<Transfer>> getRecentTransfersLiveData(int limit);

    /**
     * 插入转账记录
     * @param transfer 转账记录
     * @return 插入的行ID
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insertTransfer(Transfer transfer);

    /**
     * 批量插入转账记录
     * @param transfers 转账记录列表
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    void insertTransfers(List<Transfer> transfers);

    /**
     * 更新转账记录
     * @param transfer 转账记录
     */
    @Update
    void updateTransfer(Transfer transfer);

    /**
     * 删除转账记录
     * @param transfer 转账记录
     */
    @Delete
    void deleteTransfer(Transfer transfer);

    /**
     * 根据ID删除转账记录
     * @param transferId 转账ID
     */
    @Query("DELETE FROM transfers WHERE id = :transferId")
    void deleteTransferById(String transferId);

    /**
     * 获取转账记录数量
     * @return 转账记录数量
     */
    @Query("SELECT COUNT(*) FROM transfers")
    int getTransferCount();

    /**
     * 搜索转账记录
     * @param keyword 关键词
     * @return 转账记录列表
     */
    @Query("SELECT * FROM transfers WHERE description LIKE '%' || :keyword || '%' ORDER BY date DESC, created_at DESC")
    List<Transfer> searchTransfers(String keyword);
}
