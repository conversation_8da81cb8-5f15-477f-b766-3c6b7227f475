<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 账户名称 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="@string/account_name"
        app:startIconDrawable="@drawable/ic_account"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_account_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 账户类型 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="@string/account_type"
        app:startIconDrawable="@drawable/ic_category"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

        <AutoCompleteTextView
            android:id="@+id/spinner_account_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:focusable="false" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 初始余额 -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:hint="@string/initial_balance"
        app:startIconDrawable="@drawable/ic_money"
        app:suffixText="CNY"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_initial_balance"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="numberDecimal"
            android:maxLines="1"
            android:text="@string/default_initial_balance" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 说明文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/initial_balance_hint"
        android:textSize="12sp"
        android:textColor="?attr/colorOnSurfaceVariant"
        android:alpha="0.7" />

</LinearLayout>
