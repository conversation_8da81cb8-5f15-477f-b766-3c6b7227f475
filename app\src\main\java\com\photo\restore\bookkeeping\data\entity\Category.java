package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.util.Date;
import java.util.UUID;

/**
 * 分类实体类
 */
@Entity(tableName = "categories")
public class Category {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "name")
    private String name;
    
    @ColumnInfo(name = "type")
    private TransactionType type;
    
    @ColumnInfo(name = "icon_name")
    private String iconName;
    
    @ColumnInfo(name = "color")
    private String color;
    
    @ColumnInfo(name = "parent_category_id")
    private String parentCategoryId; // 支持二级分类
    
    @ColumnInfo(name = "is_system_default")
    private boolean isSystemDefault;
    
    @ColumnInfo(name = "is_active")
    private boolean isActive;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;

    // 构造函数
    public Category() {
        this.id = UUID.randomUUID().toString();
        this.isActive = true;
        this.isSystemDefault = false;
        this.createdAt = new Date();
    }

    public Category(String name, TransactionType type, String iconName, String color) {
        this();
        this.name = name;
        this.type = type;
        this.iconName = iconName;
        this.color = color;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TransactionType getType() {
        return type;
    }

    public void setType(TransactionType type) {
        this.type = type;
    }

    public String getIconName() {
        return iconName;
    }

    public void setIconName(String iconName) {
        this.iconName = iconName;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(String parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public boolean isSystemDefault() {
        return isSystemDefault;
    }

    public void setSystemDefault(boolean systemDefault) {
        isSystemDefault = systemDefault;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 检查是否为顶级分类
     * @return true如果是顶级分类
     */
    public boolean isTopLevel() {
        return parentCategoryId == null || parentCategoryId.isEmpty();
    }

    /**
     * 获取交易类型的显示名称
     * @param isEnglish 是否为英文
     * @return 显示名称
     */
    public String getTypeDisplayName(boolean isEnglish) {
        return type != null ? type.getDisplayName(isEnglish) : "";
    }
}
