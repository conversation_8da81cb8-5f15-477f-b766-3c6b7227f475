package com.photo.restore.bookkeeping.ui.statistics;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.photo.restore.bookkeeping.data.dao.TransactionDao;
import com.photo.restore.bookkeeping.data.repository.TransactionRepository;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 统计分析ViewModel
 * 管理统计分析相关的数据和业务逻辑
 */
public class StatisticsViewModel extends AndroidViewModel {

    /**
     * 统计周期枚举
     */
    public enum Period {
        MONTH, YEAR
    }

    private final TransactionRepository transactionRepository;
    
    // 当前统计周期
    private final MutableLiveData<Period> currentPeriod = new MutableLiveData<>();
    
    // 统计数据
    private final MediatorLiveData<BigDecimal> totalIncome = new MediatorLiveData<>();
    private final MediatorLiveData<BigDecimal> totalExpense = new MediatorLiveData<>();
    private final MediatorLiveData<List<TransactionDao.CategoryExpenseStatistic>> categoryStatistics = new MediatorLiveData<>();

    // 当前数据源引用，用于移除旧的数据源
    private LiveData<BigDecimal> currentIncomeSource;
    private LiveData<BigDecimal> currentExpenseSource;
    
    // 日期范围
    private Date startDate;
    private Date endDate;

    public StatisticsViewModel(@NonNull Application application) {
        super(application);
        
        // 初始化Repository
        transactionRepository = new TransactionRepository(application);
        
        // 设置默认周期为本月
        setPeriod(Period.MONTH);
        
        // 设置数据源
        setupDataSources();
    }

    /**
     * 设置统计周期
     */
    public void setPeriod(Period period) {
        currentPeriod.setValue(period);
        updateDateRange(period);
        refreshStatistics();
    }

    /**
     * 更新日期范围
     */
    private void updateDateRange(Period period) {
        Calendar calendar = Calendar.getInstance();
        
        if (period == Period.MONTH) {
            // 本月
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            startDate = calendar.getTime();
            
            calendar.add(Calendar.MONTH, 1);
            calendar.add(Calendar.MILLISECOND, -1);
            endDate = calendar.getTime();
            
        } else {
            // 本年
            calendar.set(Calendar.MONTH, Calendar.JANUARY);
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            startDate = calendar.getTime();
            
            calendar.set(Calendar.MONTH, Calendar.DECEMBER);
            calendar.set(Calendar.DAY_OF_MONTH, 31);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            endDate = calendar.getTime();
        }
    }

    /**
     * 设置数据源
     */
    private void setupDataSources() {
        // 监听周期变化，更新统计数据
        currentPeriod.observeForever(period -> refreshStatistics());
    }

    /**
     * 刷新统计数据
     */
    private void refreshStatistics() {
        if (startDate != null && endDate != null) {
            // 移除旧的数据源
            if (currentIncomeSource != null) {
                totalIncome.removeSource(currentIncomeSource);
            }
            if (currentExpenseSource != null) {
                totalExpense.removeSource(currentExpenseSource);
            }

            // 更新收入统计
            currentIncomeSource = transactionRepository.getTotalIncomeLiveData(startDate, endDate);
            totalIncome.addSource(currentIncomeSource, income -> {
                totalIncome.setValue(income != null ? income : BigDecimal.ZERO);
            });

            // 更新支出统计
            currentExpenseSource = transactionRepository.getTotalExpenseLiveData(startDate, endDate);
            totalExpense.addSource(currentExpenseSource, expense -> {
                totalExpense.setValue(expense != null ? expense : BigDecimal.ZERO);
            });

            // 更新分类统计
            // 注意：这里需要在后台线程执行，因为不是LiveData
            new Thread(() -> {
                List<TransactionDao.CategoryExpenseStatistic> statistics =
                    transactionRepository.getExpenseStatisticsByCategory(startDate, endDate);
                categoryStatistics.postValue(statistics);
            }).start();
        }
    }

    /**
     * 获取当前周期
     */
    public LiveData<Period> getCurrentPeriod() {
        return currentPeriod;
    }

    /**
     * 获取总收入
     */
    public LiveData<BigDecimal> getTotalIncome() {
        return totalIncome;
    }

    /**
     * 获取总支出
     */
    public LiveData<BigDecimal> getTotalExpense() {
        return totalExpense;
    }

    /**
     * 获取分类统计
     */
    public LiveData<List<TransactionDao.CategoryExpenseStatistic>> getCategoryStatistics() {
        return categoryStatistics;
    }

    /**
     * 获取净收入（收入-支出）
     */
    public LiveData<BigDecimal> getNetIncome() {
        MediatorLiveData<BigDecimal> netIncome = new MediatorLiveData<>();
        
        netIncome.addSource(totalIncome, income -> {
            BigDecimal currentIncome = income != null ? income : BigDecimal.ZERO;
            BigDecimal currentExpense = totalExpense.getValue() != null ? totalExpense.getValue() : BigDecimal.ZERO;
            netIncome.setValue(currentIncome.subtract(currentExpense));
        });
        
        netIncome.addSource(totalExpense, expense -> {
            BigDecimal currentIncome = totalIncome.getValue() != null ? totalIncome.getValue() : BigDecimal.ZERO;
            BigDecimal currentExpense = expense != null ? expense : BigDecimal.ZERO;
            netIncome.setValue(currentIncome.subtract(currentExpense));
        });
        
        return netIncome;
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        refreshStatistics();
    }

    /**
     * 获取当前日期范围的开始日期
     */
    public Date getStartDate() {
        return startDate;
    }

    /**
     * 获取当前日期范围的结束日期
     */
    public Date getEndDate() {
        return endDate;
    }
}
