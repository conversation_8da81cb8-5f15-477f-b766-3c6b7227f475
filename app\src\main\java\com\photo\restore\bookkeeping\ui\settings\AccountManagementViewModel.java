package com.photo.restore.bookkeeping.ui.settings;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.repository.AccountRepository;

import java.util.List;

/**
 * 账户管理ViewModel
 */
public class AccountManagementViewModel extends AndroidViewModel {

    private final AccountRepository accountRepository;

    public AccountManagementViewModel(@NonNull Application application) {
        super(application);
        accountRepository = new AccountRepository(application);
    }

    /**
     * 获取所有账户
     */
    public LiveData<List<Account>> getAllAccounts() {
        return accountRepository.getAllAccountsLiveData();
    }

    /**
     * 添加账户
     */
    public void addAccount(Account account) {
        accountRepository.insertAccount(account);
    }

    /**
     * 更新账户
     */
    public void updateAccount(Account account) {
        accountRepository.updateAccount(account);
    }

    /**
     * 删除账户
     */
    public void deleteAccount(Account account) {
        accountRepository.deleteAccount(account);
    }

    /**
     * 切换账户状态
     */
    public void toggleAccountStatus(Account account) {
        account.setActive(!account.isActive());
        accountRepository.updateAccount(account);
    }
}
