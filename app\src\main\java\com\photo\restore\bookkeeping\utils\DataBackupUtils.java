package com.photo.restore.bookkeeping.utils;

import android.content.Context;
import android.widget.Toast;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.entity.Budget;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.entity.ExchangeRate;
import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.Transfer;
import com.photo.restore.bookkeeping.data.entity.UserSettings;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据备份和恢复工具类
 */
public class DataBackupUtils {
    
    private static final String BACKUP_FOLDER = "backups";
    private static final String BACKUP_FILE_PREFIX = "money_backup_";
    private static final String BACKUP_FILE_EXTENSION = ".json";
    
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();
    
    /**
     * 备份数据模型
     */
    public static class BackupData {
        public List<Account> accounts;
        public List<Category> categories;
        public List<Transaction> transactions;
        public List<Transfer> transfers;
        public List<Budget> budgets;
        public List<ExchangeRate> exchangeRates;
        public List<UserSettings> userSettings;
        public String backupTime;
        public String appVersion;
        
        public BackupData() {
            this.backupTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date());
        }
    }
    
    /**
     * 执行数据备份
     */
    public static void performBackup(Context context, BackupCallback callback) {
        if (!DataExportUtils.hasStoragePermission(context)) {
            callback.onError(context.getString(R.string.storage_permission_required));
            return;
        }
        
        executor.execute(() -> {
            try {
                BookkeepingDatabase database = BookkeepingDatabase.getInstance(context);
                
                // 收集所有数据
                BackupData backupData = new BackupData();
                backupData.accounts = database.accountDao().getAllActiveAccounts();
                backupData.categories = database.categoryDao().getAllActiveCategories();
                backupData.transactions = database.transactionDao().getAllTransactions();
                backupData.transfers = database.transferDao().getAllTransfers();
                backupData.budgets = database.budgetDao().getAllActiveBudgets();
                backupData.exchangeRates = database.exchangeRateDao().getAllExchangeRates();
                
                // 获取应用版本
                try {
                    backupData.appVersion = context.getPackageManager()
                            .getPackageInfo(context.getPackageName(), 0).versionName;
                } catch (Exception e) {
                    backupData.appVersion = "Unknown";
                }
                
                // 创建备份文件
                File backupFile = createBackupFile(context);
                
                // 序列化为JSON
                Gson gson = new GsonBuilder()
                        .setPrettyPrinting()
                        .setDateFormat("yyyy-MM-dd HH:mm:ss")
                        .create();
                
                try (FileWriter writer = new FileWriter(backupFile)) {
                    gson.toJson(backupData, writer);
                }
                
                // 回调成功
                callback.onSuccess(backupFile.getAbsolutePath());
                
            } catch (Exception e) {
                callback.onError(context.getString(R.string.backup_failed, e.getMessage()));
            }
        });
    }
    
    /**
     * 恢复数据
     */
    public static void restoreData(Context context, String backupFilePath, RestoreCallback callback) {
        if (!DataExportUtils.hasStoragePermission(context)) {
            callback.onError(context.getString(R.string.storage_permission_required));
            return;
        }
        
        executor.execute(() -> {
            try {
                File backupFile = new File(backupFilePath);
                if (!backupFile.exists()) {
                    callback.onError(context.getString(R.string.backup_file_not_found));
                    return;
                }
                
                // 读取备份文件
                Gson gson = new GsonBuilder()
                        .setDateFormat("yyyy-MM-dd HH:mm:ss")
                        .create();
                
                BackupData backupData;
                try (FileReader reader = new FileReader(backupFile)) {
                    backupData = gson.fromJson(reader, BackupData.class);
                }
                
                if (backupData == null) {
                    callback.onError(context.getString(R.string.invalid_backup_file));
                    return;
                }
                
                BookkeepingDatabase database = BookkeepingDatabase.getInstance(context);
                
                // 清空现有数据（可选，根据需求决定）
                // clearAllData(database);
                
                // 恢复数据
                if (backupData.accounts != null && !backupData.accounts.isEmpty()) {
                    database.accountDao().insertAccounts(backupData.accounts);
                }
                
                if (backupData.categories != null && !backupData.categories.isEmpty()) {
                    database.categoryDao().insertCategories(backupData.categories);
                }
                
                if (backupData.transactions != null && !backupData.transactions.isEmpty()) {
                    database.transactionDao().insertTransactions(backupData.transactions);
                }
                
                if (backupData.transfers != null && !backupData.transfers.isEmpty()) {
                    database.transferDao().insertTransfers(backupData.transfers);
                }
                
                if (backupData.budgets != null && !backupData.budgets.isEmpty()) {
                    database.budgetDao().insertBudgets(backupData.budgets);
                }
                
                if (backupData.exchangeRates != null && !backupData.exchangeRates.isEmpty()) {
                    database.exchangeRateDao().insertExchangeRates(backupData.exchangeRates);
                }
                
                callback.onSuccess(context.getString(R.string.restore_success));
                
            } catch (Exception e) {
                callback.onError(context.getString(R.string.restore_failed, e.getMessage()));
            }
        });
    }
    
    /**
     * 获取备份文件列表
     */
    public static List<File> getBackupFiles(Context context) {
        File backupDir = getBackupDirectory(context);
        if (!backupDir.exists()) {
            return null;
        }
        
        File[] files = backupDir.listFiles((dir, name) -> 
            name.startsWith(BACKUP_FILE_PREFIX) && name.endsWith(BACKUP_FILE_EXTENSION));
        
        if (files == null) {
            return null;
        }
        
        return java.util.Arrays.asList(files);
    }
    
    /**
     * 删除备份文件
     */
    public static boolean deleteBackupFile(String filePath) {
        File file = new File(filePath);
        return file.exists() && file.delete();
    }
    
    /**
     * 创建备份文件
     */
    private static File createBackupFile(Context context) throws IOException {
        File backupDir = getBackupDirectory(context);
        if (!backupDir.exists()) {
            backupDir.mkdirs();
        }
        
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
        String fileName = BACKUP_FILE_PREFIX + timestamp + BACKUP_FILE_EXTENSION;
        
        return new File(backupDir, fileName);
    }
    
    /**
     * 获取备份目录
     */
    private static File getBackupDirectory(Context context) {
        return new File(context.getExternalFilesDir(null), BACKUP_FOLDER);
    }
    
    /**
     * 清空所有数据（谨慎使用）
     */
    private static void clearAllData(BookkeepingDatabase database) {
/*        database.transactionDao().deleteAllTransactions();
        database.transferDao().deleteAllTransfers();
        database.budgetDao().deleteAllBudgets();
        database.exchangeRateDao().deleteAllExchangeRates();*/
        // 注意：不删除账户和分类，因为它们可能被引用
    }
    
    /**
     * 备份回调接口
     */
    public interface BackupCallback {
        void onSuccess(String backupFilePath);
        void onError(String errorMessage);
    }
    
    /**
     * 恢复回调接口
     */
    public interface RestoreCallback {
        void onSuccess(String message);
        void onError(String errorMessage);
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format(Locale.getDefault(), "%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
    
    /**
     * 获取备份文件信息
     */
    public static String getBackupFileInfo(File backupFile) {
        if (!backupFile.exists()) {
            return "文件不存在";
        }
        
        String size = formatFileSize(backupFile.length());
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                .format(new Date(backupFile.lastModified()));
        
        return String.format(Locale.getDefault(), "%s | %s", size, date);
    }
}
