package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 转账记录实体类
 */
@Entity(
    tableName = "transfers",
    foreignKeys = {
        @ForeignKey(
            entity = Account.class,
            parentColumns = "id",
            childColumns = "from_account_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = Account.class,
            parentColumns = "id",
            childColumns = "to_account_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index("from_account_id"),
        @Index("to_account_id"),
        @Index("date")
    }
)
public class Transfer {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "from_account_id")
    private String fromAccountId;
    
    @ColumnInfo(name = "to_account_id")
    private String toAccountId;
    
    @ColumnInfo(name = "amount")
    private BigDecimal amount;
    
    @ColumnInfo(name = "fee")
    private BigDecimal fee;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "date")
    private Date date;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;

    // 构造函数
    public Transfer() {
        this.id = UUID.randomUUID().toString();
        this.amount = BigDecimal.ZERO;
        this.fee = BigDecimal.ZERO;
        this.date = new Date();
        this.createdAt = new Date();
    }

    public Transfer(String fromAccountId, String toAccountId, BigDecimal amount, String description) {
        this();
        this.fromAccountId = fromAccountId;
        this.toAccountId = toAccountId;
        this.amount = amount;
        this.description = description;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getFromAccountId() {
        return fromAccountId;
    }

    public void setFromAccountId(String fromAccountId) {
        this.fromAccountId = fromAccountId;
    }

    public String getToAccountId() {
        return toAccountId;
    }

    public void setToAccountId(String toAccountId) {
        this.toAccountId = toAccountId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * 获取实际转账金额（包含手续费）
     * @return 实际转账金额
     */
    public BigDecimal getTotalAmount() {
        return amount.add(fee != null ? fee : BigDecimal.ZERO);
    }
}
