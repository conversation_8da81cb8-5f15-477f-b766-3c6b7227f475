package com.photo.restore.bookkeeping.ui.transactions;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.photo.restore.bookkeeping.R;

import com.photo.restore.bookkeeping.data.entity.Transaction;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.data.repository.TransactionRepository;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 交易列表ViewModel
 * 管理交易列表相关的数据和业务逻辑
 */
public class TransactionsViewModel extends AndroidViewModel {

    private final TransactionRepository transactionRepository;
    
    // 筛选条件
    private final MutableLiveData<Date> startDate = new MutableLiveData<>();
    private final MutableLiveData<Date> endDate = new MutableLiveData<>();
    private final MutableLiveData<TransactionType> filterType = new MutableLiveData<>();
    private final MutableLiveData<String> searchKeyword = new MutableLiveData<>();
    
    // 筛选文本显示
    private final MutableLiveData<String> dateFilterText = new MutableLiveData<>();
    private final MutableLiveData<String> typeFilterText = new MutableLiveData<>();
    
    // 交易列表
    private final MediatorLiveData<List<TransactionWithDetails>> transactions = new MediatorLiveData<>();

    public TransactionsViewModel(@NonNull Application application) {
        super(application);
        
        // 初始化Repository
        transactionRepository = new TransactionRepository(application);
        
        // 设置默认筛选条件（当前月份）
        setCurrentMonthFilter();
        
        // 设置交易列表数据源
        setupTransactionsData();
    }

    /**
     * 设置当前月份筛选
     */
    private void setCurrentMonthFilter() {
        Calendar calendar = Calendar.getInstance();
        
        // 月初
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        startDate.setValue(calendar.getTime());
        
        // 月末
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.MILLISECOND, -1);
        endDate.setValue(calendar.getTime());
        
        // 设置显示文本
        dateFilterText.setValue(getApplication().getString(R.string.filter_this_month));
        typeFilterText.setValue(getApplication().getString(R.string.all_types));
    }

    /**
     * 设置交易列表数据源
     */
    private void setupTransactionsData() {
        // 监听筛选条件变化，更新交易列表
        transactions.addSource(startDate, this::updateTransactions);
        transactions.addSource(endDate, this::updateTransactions);
        transactions.addSource(filterType, this::updateTransactions);
        transactions.addSource(searchKeyword, this::updateTransactions);
        
        // 初始加载
        updateTransactions(null);
    }

    /**
     * 更新交易列表
     */
    private void updateTransactions(Object trigger) {
        Date start = startDate.getValue();
        Date end = endDate.getValue();
        TransactionType type = filterType.getValue();
        String keyword = searchKeyword.getValue();

        // 如果有搜索关键词，使用搜索功能
        if (keyword != null && !keyword.isEmpty()) {
            // TODO: 实现搜索功能，这里暂时使用所有交易然后在适配器中过滤
            LiveData<List<TransactionWithDetails>> source = transactionRepository.getAllTransactionsWithDetailsLiveData();
            transactions.addSource(source, allTransactions -> {
                if (allTransactions != null) {
                    List<TransactionWithDetails> filteredTransactions = new ArrayList<>();
                    for (TransactionWithDetails transaction : allTransactions) {
                        if (transaction.transaction.getDescription().toLowerCase().contains(keyword.toLowerCase()) ||
                            (transaction.transaction.getNote() != null &&
                             transaction.transaction.getNote().toLowerCase().contains(keyword.toLowerCase())) ||
                            transaction.category.getName().toLowerCase().contains(keyword.toLowerCase()) ||
                            transaction.account.getName().toLowerCase().contains(keyword.toLowerCase())) {
                            filteredTransactions.add(transaction);
                        }
                    }
                    transactions.setValue(filteredTransactions);
                } else {
                    transactions.setValue(new ArrayList<>());
                }
            });
            return;
        }

        if (start != null && end != null) {
            LiveData<List<TransactionWithDetails>> source;

            if (type != null) {
                // 按类型和日期范围筛选
                source = transactionRepository.getTransactionsWithDetailsByDateRangeAndTypeLiveData(start, end, type);
            } else {
                // 按日期范围筛选
                source = transactionRepository.getTransactionsWithDetailsByDateRangeLiveData(start, end);
            }

            transactions.addSource(source, transactions::setValue);
        } else {
            // 获取所有交易
            LiveData<List<TransactionWithDetails>> source = transactionRepository.getAllTransactionsWithDetailsLiveData();
            transactions.addSource(source, transactions::setValue);
        }
    }

    /**
     * 获取交易列表
     */
    public LiveData<List<TransactionWithDetails>> getTransactions() {
        return transactions;
    }

    /**
     * 获取日期筛选文本
     */
    public LiveData<String> getDateFilterText() {
        return dateFilterText;
    }

    /**
     * 获取类型筛选文本
     */
    public LiveData<String> getTypeFilterText() {
        return typeFilterText;
    }

    /**
     * 设置日期筛选
     */
    public void setDateFilter(Date start, Date end, String displayText) {
        startDate.setValue(start);
        endDate.setValue(end);
        dateFilterText.setValue(displayText);
    }

    /**
     * 设置类型筛选
     */
    public void setTypeFilter(TransactionType type, String displayText) {
        filterType.setValue(type);
        typeFilterText.setValue(displayText);
    }

    /**
     * 清除类型筛选
     */
    public void clearTypeFilter() {
        filterType.setValue(null);
        typeFilterText.setValue(getApplication().getString(R.string.all_types));
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        // 触发数据更新
        updateTransactions(null);
    }

    /**
     * 设置本月筛选
     */
    public void setCurrentMonth() {
        setCurrentMonthFilter();
    }

    /**
     * 设置本年筛选
     */
    public void setCurrentYear() {
        Calendar calendar = Calendar.getInstance();
        
        // 年初
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        startDate.setValue(calendar.getTime());
        
        // 年末
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        endDate.setValue(calendar.getTime());
        
        dateFilterText.setValue(getApplication().getString(R.string.filter_year));
    }

    /**
     * 删除交易
     */
    public void deleteTransaction(String transactionId) {
        transactionRepository.deleteTransactionById(transactionId);
    }

    /**
     * 设置搜索关键词
     */
    public void setSearchKeyword(String keyword) {
        searchKeyword.setValue(keyword);
    }

    /**
     * 获取搜索关键词
     */
    public LiveData<String> getSearchKeyword() {
        return searchKeyword;
    }
}
