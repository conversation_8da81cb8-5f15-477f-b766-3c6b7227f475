<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 原型界面总览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ios-blue': '#007AFF',
                        'ios-green': '#34C759',
                        'ios-red': '#FF3B30',
                        'ios-orange': '#FF9500',
                        'ios-purple': '#AF52DE',
                        'ios-gray': '#8E8E93',
                        'ios-gray-2': '#AEAEB2',
                        'ios-gray-3': '#C7C7CC',
                        'ios-gray-4': '#D1D1D6',
                        'ios-gray-5': '#E5E5EA',
                        'ios-gray-6': '#F2F2F7'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-frame {
            width: 375px;
            height: 812px;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            border-radius: 32px;
            overflow: hidden;
            background: white;
        }
        .prototype-link {
            transition: all 0.3s ease;
        }
        .prototype-link:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen p-8">
    <div class="max-w-7xl mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">
                <i class="fas fa-mobile-alt text-ios-blue mr-3"></i>
                Money App 原型界面
            </h1>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                全球记账应用的高保真原型界面展示，支持中英文本地化，符合iOS设计规范
            </p>
        </div>

        <!-- 界面网格 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 justify-items-center">
            
            <!-- 启动页面 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('splash.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-gradient-to-b from-ios-blue to-blue-600 flex flex-col items-center justify-center text-white">
                        <div class="w-24 h-24 bg-white rounded-3xl flex items-center justify-center mb-6 shadow-lg">
                            <i class="fas fa-wallet text-ios-blue text-4xl"></i>
                        </div>
                        <h2 class="text-3xl font-bold mb-2">Money</h2>
                        <p class="text-blue-100 text-center px-8">智能记账，理财无忧</p>
                        <div class="mt-8">
                            <div class="animate-spin w-6 h-6 border-2 border-white border-t-transparent rounded-full"></div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">启动页面</h3>
                    <p class="text-sm text-gray-600">Splash Screen</p>
                </div>
            </div>

            <!-- 主页面 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('home.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-ios-gray-6">
                        <!-- 状态栏 -->
                        <div class="h-11 bg-white flex items-center justify-between px-6 text-black text-sm font-medium">
                            <span>9:41</span>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-signal text-xs"></i>
                                <i class="fas fa-wifi text-xs"></i>
                                <i class="fas fa-battery-three-quarters text-xs"></i>
                            </div>
                        </div>
                        <!-- 内容区域 -->
                        <div class="flex-1 px-4 pt-6">
                            <!-- 总余额卡片 -->
                            <div class="bg-gradient-to-r from-ios-blue to-blue-600 rounded-2xl p-6 text-white mb-6">
                                <p class="text-blue-100 mb-2">总资产</p>
                                <h2 class="text-3xl font-bold mb-4">¥12,580.50</h2>
                                <div class="flex justify-between text-sm">
                                    <div>
                                        <p class="text-blue-100">本月收入</p>
                                        <p class="font-semibold">+¥8,500</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-blue-100">本月支出</p>
                                        <p class="font-semibold">-¥3,240</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 快速操作 -->
                            <div class="flex space-x-4 mb-6">
                                <div class="flex-1 bg-white rounded-xl p-4 text-center">
                                    <div class="w-12 h-12 bg-ios-green rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-plus text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium">记收入</p>
                                </div>
                                <div class="flex-1 bg-white rounded-xl p-4 text-center">
                                    <div class="w-12 h-12 bg-ios-red rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-minus text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium">记支出</p>
                                </div>
                                <div class="flex-1 bg-white rounded-xl p-4 text-center">
                                    <div class="w-12 h-12 bg-ios-purple rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-exchange-alt text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium">转账</p>
                                </div>
                            </div>
                            <!-- 近期交易 -->
                            <div class="bg-white rounded-xl p-4">
                                <h3 class="font-semibold mb-3">近期交易</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-ios-orange rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-utensils text-white text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium">午餐</p>
                                            <p class="text-xs text-gray-500">今天 12:30</p>
                                        </div>
                                        <span class="text-ios-red font-semibold">-¥35.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 底部导航 -->
                        <div class="h-20 bg-white border-t border-ios-gray-5 flex">
                            <div class="flex-1 flex flex-col items-center justify-center text-ios-blue">
                                <i class="fas fa-home text-lg mb-1"></i>
                                <span class="text-xs">首页</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                                <i class="fas fa-list text-lg mb-1"></i>
                                <span class="text-xs">账单</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                                <i class="fas fa-chart-pie text-lg mb-1"></i>
                                <span class="text-xs">统计</span>
                            </div>
                            <div class="flex-1 flex flex-col items-center justify-center text-ios-gray">
                                <i class="fas fa-cog text-lg mb-1"></i>
                                <span class="text-xs">设置</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">主页面</h3>
                    <p class="text-sm text-gray-600">Home Screen</p>
                </div>
            </div>

            <!-- 记账页面 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('add-transaction.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-white">
                        <!-- 导航栏 -->
                        <div class="h-20 bg-white flex items-end pb-4 px-4 border-b border-ios-gray-5">
                            <div class="flex items-center justify-between w-full">
                                <button class="text-ios-blue">取消</button>
                                <h1 class="font-semibold text-lg">记一笔</h1>
                                <button class="text-ios-blue font-semibold">完成</button>
                            </div>
                        </div>
                        <!-- 金额输入 -->
                        <div class="text-center py-8">
                            <div class="text-6xl font-light text-gray-800 mb-2">¥ 0</div>
                            <p class="text-ios-gray">输入金额</p>
                        </div>
                        <!-- 收支切换 -->
                        <div class="flex mx-4 mb-6">
                            <button class="flex-1 py-3 bg-ios-red text-white rounded-l-xl font-medium">支出</button>
                            <button class="flex-1 py-3 bg-ios-gray-5 text-gray-600 rounded-r-xl font-medium">收入</button>
                        </div>
                        <!-- 分类选择 -->
                        <div class="px-4 mb-6">
                            <h3 class="font-semibold mb-3">选择分类</h3>
                            <div class="grid grid-cols-4 gap-4">
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-ios-orange rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-utensils text-white"></i>
                                    </div>
                                    <p class="text-xs">餐饮</p>
                                </div>
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-ios-blue rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-bus text-white"></i>
                                    </div>
                                    <p class="text-xs">交通</p>
                                </div>
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-ios-purple rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-gamepad text-white"></i>
                                    </div>
                                    <p class="text-xs">娱乐</p>
                                </div>
                                <div class="text-center">
                                    <div class="w-12 h-12 bg-ios-green rounded-full flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-shopping-bag text-white"></i>
                                    </div>
                                    <p class="text-xs">购物</p>
                                </div>
                            </div>
                        </div>
                        <!-- 数字键盘 -->
                        <div class="absolute bottom-0 left-0 right-0 bg-ios-gray-6 p-4">
                            <div class="grid grid-cols-3 gap-2">
                                <button class="h-12 bg-white rounded-lg font-semibold">1</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">2</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">3</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">4</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">5</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">6</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">7</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">8</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">9</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">.</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">0</button>
                                <button class="h-12 bg-white rounded-lg font-semibold">
                                    <i class="fas fa-backspace"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">记账页面</h3>
                    <p class="text-sm text-gray-600">Add Transaction</p>
                </div>
            </div>

            <!-- 交易列表 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('transactions.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-ios-gray-6">
                        <!-- 导航栏 -->
                        <div class="h-20 bg-white flex items-end pb-4 px-4">
                            <h1 class="font-semibold text-xl">账单</h1>
                        </div>
                        <!-- 筛选条件 -->
                        <div class="bg-white px-4 py-3 border-b border-ios-gray-5">
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 bg-ios-blue text-white rounded-full text-sm">全部</button>
                                <button class="px-4 py-2 bg-ios-gray-5 text-gray-600 rounded-full text-sm">收入</button>
                                <button class="px-4 py-2 bg-ios-gray-5 text-gray-600 rounded-full text-sm">支出</button>
                                <button class="px-4 py-2 bg-ios-gray-5 text-gray-600 rounded-full text-sm">
                                    <i class="fas fa-filter mr-1"></i>筛选
                                </button>
                            </div>
                        </div>
                        <!-- 交易列表 -->
                        <div class="flex-1 overflow-y-auto">
                            <!-- 今天 -->
                            <div class="px-4 py-3">
                                <h3 class="text-sm font-semibold text-gray-600 mb-3">今天</h3>
                                <div class="bg-white rounded-xl p-4 space-y-3">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-ios-orange rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-utensils text-white text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium">午餐 - 沙县小吃</p>
                                            <p class="text-xs text-gray-500">餐饮 · 现金 · 12:30</p>
                                        </div>
                                        <span class="text-ios-red font-semibold">-¥35.00</span>
                                    </div>
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-ios-blue rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-bus text-white text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium">地铁</p>
                                            <p class="text-xs text-gray-500">交通 · 支付宝 · 08:30</p>
                                        </div>
                                        <span class="text-ios-red font-semibold">-¥4.00</span>
                                    </div>
                                </div>
                            </div>
                            <!-- 昨天 -->
                            <div class="px-4 py-3">
                                <h3 class="text-sm font-semibold text-gray-600 mb-3">昨天</h3>
                                <div class="bg-white rounded-xl p-4 space-y-3">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-ios-green rounded-full flex items-center justify-center mr-3">
                                            <i class="fas fa-hand-holding-usd text-white text-sm"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium">工资</p>
                                            <p class="text-xs text-gray-500">薪资收入 · 银行卡 · 09:00</p>
                                        </div>
                                        <span class="text-ios-green font-semibold">+¥8,500.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">交易列表</h3>
                    <p class="text-sm text-gray-600">Transaction List</p>
                </div>
            </div>

            <!-- 统计页面 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('statistics.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-ios-gray-6">
                        <!-- 导航栏 -->
                        <div class="h-20 bg-white flex items-end pb-4 px-4">
                            <h1 class="font-semibold text-xl">统计</h1>
                        </div>
                        <!-- 时间选择 -->
                        <div class="bg-white px-4 py-3 border-b border-ios-gray-5">
                            <div class="flex justify-center space-x-4">
                                <button class="px-4 py-2 bg-ios-blue text-white rounded-full text-sm">本月</button>
                                <button class="px-4 py-2 bg-ios-gray-5 text-gray-600 rounded-full text-sm">本年</button>
                                <button class="px-4 py-2 bg-ios-gray-5 text-gray-600 rounded-full text-sm">自定义</button>
                            </div>
                        </div>
                        <!-- 收支概览 -->
                        <div class="px-4 py-4">
                            <div class="bg-white rounded-xl p-4 mb-4">
                                <h3 class="font-semibold mb-4">2024年6月收支概览</h3>
                                <div class="flex justify-between mb-4">
                                    <div class="text-center">
                                        <p class="text-ios-green text-2xl font-bold">¥8,500</p>
                                        <p class="text-sm text-gray-500">收入</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-ios-red text-2xl font-bold">¥3,240</p>
                                        <p class="text-sm text-gray-500">支出</p>
                                    </div>
                                    <div class="text-center">
                                        <p class="text-ios-blue text-2xl font-bold">¥5,260</p>
                                        <p class="text-sm text-gray-500">结余</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 支出分析 -->
                            <div class="bg-white rounded-xl p-4 mb-4">
                                <h3 class="font-semibold mb-4">支出分析</h3>
                                <!-- 饼图占位 -->
                                <div class="w-32 h-32 bg-gradient-to-br from-ios-orange via-ios-blue to-ios-purple rounded-full mx-auto mb-4"></div>
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-ios-orange rounded-full mr-2"></div>
                                            <span class="text-sm">餐饮</span>
                                        </div>
                                        <span class="text-sm font-semibold">¥1,200 (37%)</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-ios-blue rounded-full mr-2"></div>
                                            <span class="text-sm">交通</span>
                                        </div>
                                        <span class="text-sm font-semibold">¥640 (20%)</span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="w-3 h-3 bg-ios-purple rounded-full mr-2"></div>
                                            <span class="text-sm">娱乐</span>
                                        </div>
                                        <span class="text-sm font-semibold">¥800 (25%)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">统计页面</h3>
                    <p class="text-sm text-gray-600">Statistics</p>
                </div>
            </div>

            <!-- 设置页面 -->
            <div class="prototype-link cursor-pointer" onclick="openPrototype('settings.html')">
                <div class="phone-frame">
                    <div class="phone-screen bg-ios-gray-6">
                        <!-- 导航栏 -->
                        <div class="h-20 bg-white flex items-end pb-4 px-4">
                            <h1 class="font-semibold text-xl">设置</h1>
                        </div>
                        <!-- 设置列表 -->
                        <div class="flex-1 px-4 py-4 space-y-4">
                            <!-- 账户设置 -->
                            <div class="bg-white rounded-xl overflow-hidden">
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-ios-blue rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-user text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">账户管理</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-ios-green rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-list text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">分类管理</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                                <div class="px-4 py-3 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-ios-orange rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-bullseye text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">预算设置</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                            </div>
                            <!-- 应用设置 -->
                            <div class="bg-white rounded-xl overflow-hidden">
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-ios-purple rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-globe text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">语言设置</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-ios-gray mr-2">简体中文</span>
                                        <i class="fas fa-chevron-right text-ios-gray"></i>
                                    </div>
                                </div>
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-ios-red rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-dollar-sign text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">默认货币</span>
                                    </div>
                                    <div class="flex items-center">
                                        <span class="text-ios-gray mr-2">人民币 (¥)</span>
                                        <i class="fas fa-chevron-right text-ios-gray"></i>
                                    </div>
                                </div>
                                <div class="px-4 py-3 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-600 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-moon text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">深色模式</span>
                                    </div>
                                    <div class="w-12 h-6 bg-ios-gray-3 rounded-full relative">
                                        <div class="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5"></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 数据管理 -->
                            <div class="bg-white rounded-xl overflow-hidden">
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-cloud text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">数据备份</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                                <div class="px-4 py-3 border-b border-ios-gray-5 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-download text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium">数据导出</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                                <div class="px-4 py-3 flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-trash text-white text-sm"></i>
                                        </div>
                                        <span class="font-medium text-ios-red">清除数据</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-ios-gray"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="text-center mt-4">
                    <h3 class="font-semibold text-gray-800">设置页面</h3>
                    <p class="text-sm text-gray-600">Settings</p>
                </div>
            </div>
        </div>

        <!-- 功能说明 -->
        <div class="mt-16 bg-white rounded-2xl p-8 max-w-4xl mx-auto">
            <h2 class="text-2xl font-bold text-center mb-6">原型界面特点</h2>
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold mb-3 text-ios-blue">
                        <i class="fas fa-mobile-alt mr-2"></i>iOS设计规范
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 符合iOS Human Interface Guidelines</li>
                        <li>• 原生控件样式和交互模式</li>
                        <li>• 适配iPhone屏幕尺寸 (375x812)</li>
                        <li>• 支持深色/浅色模式切换</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-3 text-ios-green">
                        <i class="fas fa-globe mr-2"></i>国际化支持
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 中英文界面本地化</li>
                        <li>• 多货币显示支持</li>
                        <li>• 文化适配的颜色方案</li>
                        <li>• 响应式布局设计</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-3 text-ios-purple">
                        <i class="fas fa-palette mr-2"></i>视觉设计
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 现代简洁的设计风格</li>
                        <li>• 清晰的信息层级结构</li>
                        <li>• 直观的图标和色彩系统</li>
                        <li>• 优雅的微交互动效</li>
                    </ul>
                </div>
                <div>
                    <h3 class="font-semibold mb-3 text-ios-orange">
                        <i class="fas fa-hand-pointer mr-2"></i>用户体验
                    </h3>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• 简化的记账操作流程</li>
                        <li>• 智能的数据可视化</li>
                        <li>• 一致的交互体验</li>
                        <li>• 无障碍访问支持</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 页面底部 -->
        <div class="text-center mt-12 text-gray-500">
            <p>点击任意界面查看详细原型 • Money App © 2024</p>
        </div>
    </div>

    <script>
        function openPrototype(filename) {
            window.open(filename, '_blank', 'width=400,height=850,scrollbars=yes,resizable=yes');
        }
    </script>
</body>
</html> 