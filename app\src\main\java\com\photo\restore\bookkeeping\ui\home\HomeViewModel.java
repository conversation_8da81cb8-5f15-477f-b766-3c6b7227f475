package com.photo.restore.bookkeeping.ui.home;

import android.app.Application;

import androidx.annotation.NonNull;
import android.util.Log;

import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.photo.restore.bookkeeping.data.repository.AccountRepository;
import com.photo.restore.bookkeeping.data.repository.TransactionRepository;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 首页ViewModel
 * 管理首页相关的数据和业务逻辑
 */
public class HomeViewModel extends AndroidViewModel {

    private final TransactionRepository transactionRepository;
    private final AccountRepository accountRepository;
    
    // LiveData
    private final MediatorLiveData<BigDecimal> totalAssets;
    private final MediatorLiveData<BigDecimal> monthlyIncome;
    private final MediatorLiveData<BigDecimal> monthlyExpense;
    private final LiveData<List<TransactionWithDetails>> recentTransactions;
    
    // 当前月份的开始和结束日期
    private final Date monthStart;
    private final Date monthEnd;

    public HomeViewModel(@NonNull Application application) {
        super(application);
        
        // 初始化Repository
        transactionRepository = new TransactionRepository(application);
        accountRepository = new AccountRepository(application);
        
        // 计算当前月份的日期范围
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        monthStart = calendar.getTime();
        
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.MILLISECOND, -1);
        monthEnd = calendar.getTime();
        
        // 初始化LiveData
        totalAssets = new MediatorLiveData<>();
        monthlyIncome = new MediatorLiveData<>();
        monthlyExpense = new MediatorLiveData<>();
        recentTransactions = transactionRepository.getRecentTransactionsWithDetailsLiveData(10);
        
        setupTotalAssets();
        setupMonthlyIncome();
        setupMonthlyExpense();
    }

    // 存储LiveData引用以便在计算时使用
    private LiveData<BigDecimal> accountsBalanceLiveData;
    private LiveData<BigDecimal> allTimeIncomeLiveData;
    private LiveData<BigDecimal> allTimeExpenseLiveData;

    /**
     * 设置总资产计算
     * 总资产 = 账户初始余额 + 所有收入 - 所有支出
     */
    private void setupTotalAssets() {
        accountsBalanceLiveData = accountRepository.getTotalBalanceLiveData();
        allTimeIncomeLiveData = transactionRepository.getAllTimeIncomeLiveData();
        allTimeExpenseLiveData = transactionRepository.getAllTimeExpenseLiveData();

        totalAssets.addSource(accountsBalanceLiveData, balance -> calculateTotalAssets());
        totalAssets.addSource(allTimeIncomeLiveData, income -> calculateTotalAssets());
        totalAssets.addSource(allTimeExpenseLiveData, expense -> calculateTotalAssets());
    }

    /**
     * 计算总资产
     */
    private void calculateTotalAssets() {
        BigDecimal balance = accountsBalanceLiveData.getValue();
        BigDecimal income = allTimeIncomeLiveData.getValue();
        BigDecimal expense = allTimeExpenseLiveData.getValue();

        Log.d("HomeViewModel", "Raw values - Balance: " + balance + ", Income: " + income + ", Expense: " + expense);

        // 如果任何一个值为null，使用0
        if (balance == null) balance = BigDecimal.ZERO;
        if (income == null) income = BigDecimal.ZERO;
        if (expense == null) expense = BigDecimal.ZERO;

        Log.d("HomeViewModel", "After null check - Balance: " + balance + ", Income: " + income + ", Expense: " + expense);

        // 计算总资产：初始余额 + 收入 - 支出
        BigDecimal total = balance.add(income).subtract(expense);
        Log.d("HomeViewModel", "Calculated total assets: " + total);

        totalAssets.setValue(total);
    }

    /**
     * 设置本月收入计算
     */
    private void setupMonthlyIncome() {
        LiveData<BigDecimal> incomeData = transactionRepository.getTotalIncomeLiveData(monthStart, monthEnd);
        monthlyIncome.addSource(incomeData, income -> {
            monthlyIncome.setValue(income != null ? income : BigDecimal.ZERO);
        });
    }

    /**
     * 设置本月支出计算
     */
    private void setupMonthlyExpense() {
        LiveData<BigDecimal> expenseData = transactionRepository.getTotalExpenseLiveData(monthStart, monthEnd);
        monthlyExpense.addSource(expenseData, expense -> {
            monthlyExpense.setValue(expense != null ? expense : BigDecimal.ZERO);
        });
    }

    /**
     * 获取总资产
     */
    public LiveData<BigDecimal> getTotalAssets() {
        return totalAssets;
    }

    /**
     * 获取本月收入
     */
    public LiveData<BigDecimal> getMonthlyIncome() {
        return monthlyIncome;
    }

    /**
     * 获取本月支出
     */
    public LiveData<BigDecimal> getMonthlyExpense() {
        return monthlyExpense;
    }

    /**
     * 获取最近交易
     */
    public LiveData<List<TransactionWithDetails>> getRecentTransactions() {
        return recentTransactions;
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        // 由于使用LiveData，数据会自动更新
        // 这里可以添加额外的刷新逻辑，比如强制重新计算
    }

    /**
     * 获取本月净收入（收入-支出）
     */
    public LiveData<BigDecimal> getMonthlyNetIncome() {
        MediatorLiveData<BigDecimal> netIncome = new MediatorLiveData<>();
        
        netIncome.addSource(monthlyIncome, income -> {
            BigDecimal currentIncome = income != null ? income : BigDecimal.ZERO;
            BigDecimal currentExpense = monthlyExpense.getValue() != null ? monthlyExpense.getValue() : BigDecimal.ZERO;
            netIncome.setValue(currentIncome.subtract(currentExpense));
        });
        
        netIncome.addSource(monthlyExpense, expense -> {
            BigDecimal currentIncome = monthlyIncome.getValue() != null ? monthlyIncome.getValue() : BigDecimal.ZERO;
            BigDecimal currentExpense = expense != null ? expense : BigDecimal.ZERO;
            netIncome.setValue(currentIncome.subtract(currentExpense));
        });
        
        return netIncome;
    }
}
