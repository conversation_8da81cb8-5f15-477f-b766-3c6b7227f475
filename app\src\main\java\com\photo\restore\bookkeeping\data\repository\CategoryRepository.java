package com.photo.restore.bookkeeping.data.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.R;

import com.photo.restore.bookkeeping.data.dao.CategoryDao;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 分类仓库类
 */
public class CategoryRepository {

    private final CategoryDao categoryDao;
    private final ExecutorService executor;
    private final LiveData<List<Category>> allActiveCategoriesLiveData;
    private final Application application;

    public CategoryRepository(Application application) {
        this.application = application;
        BookkeepingDatabase database = BookkeepingDatabase.getInstance(application);
        categoryDao = database.categoryDao();
        executor = Executors.newFixedThreadPool(2);
        allActiveCategoriesLiveData = categoryDao.getAllActiveCategoriesLiveData();
    }

    /**
     * 获取所有活跃分类（LiveData）
     * @return 分类列表LiveData
     */
    public LiveData<List<Category>> getAllActiveCategoriesLiveData() {
        return allActiveCategoriesLiveData;
    }

    /**
     * 获取所有活跃分类（同步方法）
     * @return 分类列表
     */
    public List<Category> getAllActiveCategories() {
        return categoryDao.getAllActiveCategories();
    }

    /**
     * 根据类型获取分类（LiveData）
     * @param type 交易类型
     * @return 分类列表LiveData
     */
    public LiveData<List<Category>> getCategoriesByTypeLiveData(TransactionType type) {
        return categoryDao.getCategoriesByTypeLiveData(type);
    }

    /**
     * 根据类型获取所有分类（包括非活跃）（LiveData）- 用于管理界面
     * @param type 交易类型
     * @return 分类列表LiveData
     */
    public LiveData<List<Category>> getAllCategoriesByTypeLiveData(TransactionType type) {
        return categoryDao.getAllCategoriesByTypeLiveData(type);
    }

    /**
     * 获取所有分类（LiveData）
     * @return 分类列表LiveData
     */
    public LiveData<List<Category>> getAllCategoriesLiveData() {
        return categoryDao.getAllCategoriesLiveData();
    }

    /**
     * 获取顶级分类（LiveData）
     * @param type 交易类型
     * @return 顶级分类列表LiveData
     */
    public LiveData<List<Category>> getTopLevelCategoriesLiveData(TransactionType type) {
        return categoryDao.getTopLevelCategoriesLiveData(type);
    }

    /**
     * 获取子分类（LiveData）
     * @param parentId 父分类ID
     * @return 子分类列表LiveData
     */
    public LiveData<List<Category>> getSubCategoriesLiveData(String parentId) {
        return categoryDao.getSubCategoriesLiveData(parentId);
    }

    /**
     * 根据ID获取分类（LiveData）
     * @param categoryId 分类ID
     * @return 分类LiveData
     */
    public LiveData<Category> getCategoryByIdLiveData(String categoryId) {
        return categoryDao.getCategoryByIdLiveData(categoryId);
    }

    /**
     * 根据类型获取分类（同步）
     * @param type 交易类型
     * @return 分类列表
     */
    public List<Category> getCategoriesByType(TransactionType type) {
        return categoryDao.getCategoriesByType(type);
    }

    /**
     * 获取顶级分类（同步）
     * @param type 交易类型
     * @return 顶级分类列表
     */
    public List<Category> getTopLevelCategories(TransactionType type) {
        return categoryDao.getTopLevelCategories(type);
    }

    /**
     * 获取子分类（同步）
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    public List<Category> getSubCategories(String parentId) {
        return categoryDao.getSubCategories(parentId);
    }

    /**
     * 根据ID获取分类（同步）
     * @param categoryId 分类ID
     * @return 分类
     */
    public Category getCategoryById(String categoryId) {
        return categoryDao.getCategoryById(categoryId);
    }

    /**
     * 获取系统默认分类
     * @return 系统默认分类列表
     */
    public List<Category> getSystemDefaultCategories() {
        return categoryDao.getSystemDefaultCategories();
    }

    /**
     * 根据名称搜索分类
     * @param name 分类名称
     * @return 分类列表
     */
    public List<Category> searchCategoriesByName(String name) {
        return categoryDao.searchCategoriesByName(name);
    }

    /**
     * 插入分类
     * @param category 分类
     */
    public void insertCategory(Category category) {
        executor.execute(() -> categoryDao.insertCategory(category));
    }

    /**
     * 批量插入分类
     * @param categories 分类列表
     */
    public void insertCategories(List<Category> categories) {
        executor.execute(() -> categoryDao.insertCategories(categories));
    }

    /**
     * 更新分类
     * @param category 分类
     */
    public void updateCategory(Category category) {
        executor.execute(() -> {
            categoryDao.updateCategory(category);
        });
    }

    /**
     * 软删除分类
     * @param categoryId 分类ID
     */
    public void softDeleteCategory(String categoryId) {
        executor.execute(() -> categoryDao.softDeleteCategory(categoryId));
    }

    /**
     * 删除分类
     * @param category 分类
     */
    public void deleteCategory(Category category) {
        executor.execute(() -> categoryDao.deleteCategory(category));
    }

    /**
     * 检查分类名称是否存在
     * @param name 分类名称
     * @param type 交易类型
     * @param excludeId 排除的分类ID
     * @return true如果存在
     */
    public boolean checkCategoryNameExists(String name, TransactionType type, String excludeId) {
        return categoryDao.checkCategoryNameExists(name, type, excludeId) > 0;
    }

    /**
     * 创建默认分类
     */
    public void createDefaultCategories() {
        executor.execute(() -> {
            if (categoryDao.getCategoryCount(TransactionType.EXPENSE) == 0) {
                List<Category> defaultCategories = createDefaultCategoryList();
                categoryDao.insertCategories(defaultCategories);
            }
        });
    }

    /**
     * 创建默认分类列表
     * @return 默认分类列表
     */
    private List<Category> createDefaultCategoryList() {
        List<Category> categories = new ArrayList<>();

        // 支出分类
        categories.add(createCategory(application.getString(R.string.category_food), TransactionType.EXPENSE, "food", "#FF6B6B", true));
        categories.add(createCategory(application.getString(R.string.category_transport), TransactionType.EXPENSE, "transport", "#4ECDC4", true));
        categories.add(createCategory(application.getString(R.string.category_shopping), TransactionType.EXPENSE, "shopping", "#45B7D1", true));
        categories.add(createCategory(application.getString(R.string.category_entertainment), TransactionType.EXPENSE, "entertainment", "#96CEB4", true));
        categories.add(createCategory(application.getString(R.string.category_health), TransactionType.EXPENSE, "health", "#FFEAA7", true));
        categories.add(createCategory(application.getString(R.string.category_education), TransactionType.EXPENSE, "education", "#DDA0DD", true));
        categories.add(createCategory(application.getString(R.string.category_housing), TransactionType.EXPENSE, "home", "#98D8C8", true));
        categories.add(createCategory(application.getString(R.string.category_communication), TransactionType.EXPENSE, "phone", "#FFB6C1", true));
        categories.add(createCategory(application.getString(R.string.category_other_expense), TransactionType.EXPENSE, "other", "#D3D3D3", true));

        // 收入分类
        categories.add(createCategory(application.getString(R.string.category_salary), TransactionType.INCOME, "salary", "#55A3FF", true));
        categories.add(createCategory(application.getString(R.string.category_bonus), TransactionType.INCOME, "gift", "#FFD93D", true));
        categories.add(createCategory(application.getString(R.string.category_investment), TransactionType.INCOME, "investment", "#6BCF7F", true));
        categories.add(createCategory(application.getString(R.string.category_part_time), TransactionType.INCOME, "work", "#A8E6CF", true));
        categories.add(createCategory(application.getString(R.string.category_other_income), TransactionType.INCOME, "other", "#D3D3D3", true));

        return categories;
    }

    /**
     * 创建分类对象
     * @param name 分类名称
     * @param type 交易类型
     * @param iconName 图标名称
     * @param color 颜色
     * @param isSystemDefault 是否为系统默认
     * @return 分类对象
     */
    private Category createCategory(String name, TransactionType type, String iconName, String color, boolean isSystemDefault) {
        Category category = new Category();
        category.setName(name);
        category.setType(type);
        category.setIconName(iconName);
        category.setColor(color);
        category.setSystemDefault(isSystemDefault);
        return category;
    }

    /**
     * 根据名称查找分类
     * @param name 分类名称
     * @return 分类对象，如果未找到则返回null
     */
    public Category getCategoryByName(String name) {
        return categoryDao.getCategoryByName(name);
    }

    /**
     * 根据名称和类型查找分类
     * @param name 分类名称
     * @param type 交易类型
     * @return 分类对象，如果未找到则返回null
     */
    public Category getCategoryByNameAndType(String name, TransactionType type) {
        return categoryDao.getCategoryByNameAndType(name, type);
    }
}
