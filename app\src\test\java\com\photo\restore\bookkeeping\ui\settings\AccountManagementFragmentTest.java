package com.photo.restore.bookkeeping.ui.settings;

import static org.junit.Assert.*;

import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.enums.AccountType;

import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * AccountManagementFragment的单元测试
 */
public class AccountManagementFragmentTest {

    @Before
    public void setUp() {
        // 测试设置
    }



    /**
     * 创建测试用的账户对象
     */
    private Account createTestAccount(String name, AccountType type, BigDecimal balance) {
        Account account = new Account();
        account.setName(name);
        account.setType(type);
        account.setInitialBalance(balance);
        account.setCurrentBalance(balance);
        account.setActive(true);
        account.setCurrency("CNY");
        return account;
    }

    /**
     * 测试账户列表为空的情况
     */
    @Test
    public void testEmptyAccountsList() {
        List<Account> emptyList = new ArrayList<>();
        assertTrue("空列表应该返回true", emptyList.isEmpty());
    }

    /**
     * 测试账户列表不为空的情况
     */
    @Test
    public void testNonEmptyAccountsList() {
        List<Account> accounts = Arrays.asList(
            createTestAccount("现金", AccountType.CASH, new BigDecimal("1000.00")),
            createTestAccount("银行卡", AccountType.SAVINGS_CARD, new BigDecimal("5000.00"))
        );
        assertFalse("非空列表应该返回false", accounts.isEmpty());
        assertEquals("列表应该包含2个账户", 2, accounts.size());
    }
}
