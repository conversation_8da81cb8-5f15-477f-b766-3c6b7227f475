<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.transactions.TransactionsFragment">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        app:title="@string/nav_transactions"
        app:titleTextColor="?attr/colorOnPrimary"
        app:menu="@menu/menu_transactions" />

    <!-- 搜索框 -->
    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_search"
        style="@style/Widget.Material3.TextInputLayout.OutlinedBox"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:hint="@string/search_transactions_hint"
        android:visibility="gone"
        app:startIconDrawable="@drawable/ic_search"
        app:endIconMode="clear_text">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:imeOptions="actionSearch" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- 筛选栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="?attr/colorSurface">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_filter_date"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/filter_this_month"
            app:icon="@drawable/ic_calendar" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_filter_type"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="@string/filter_all"
            app:icon="@drawable/ic_filter" />

    </LinearLayout>

    <!-- 交易列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_transactions"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        tools:listitem="@layout/item_transaction" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/empty_transactions_icon"
            android:textSize="48sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/no_data"
            android:textAppearance="@style/TextAppearance.Material3.BodyLarge"
            android:textColor="?attr/colorOnSurfaceVariant" />

    </LinearLayout>

</LinearLayout>
