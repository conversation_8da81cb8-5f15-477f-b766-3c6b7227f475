package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.photo.restore.bookkeeping.data.enums.BudgetPeriod;

import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 预算实体类
 */
@Entity(
    tableName = "budgets",
    foreignKeys = {
        @ForeignKey(
            entity = Category.class,
            parentColumns = "id",
            childColumns = "category_id",
            onDelete = ForeignKey.CASCADE
        )
    },
    indices = {
        @Index("category_id"),
        @Index("start_date"),
        @Index("end_date")
    }
)
public class Budget {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "category_id")
    private String categoryId;
    
    @ColumnInfo(name = "amount")
    private BigDecimal amount;
    
    @ColumnInfo(name = "period")
    private BudgetPeriod period;
    
    @ColumnInfo(name = "start_date")
    private Date startDate;
    
    @ColumnInfo(name = "end_date")
    private Date endDate;
    
    @ColumnInfo(name = "is_active")
    private boolean isActive;
    
    @ColumnInfo(name = "notification_enabled")
    private boolean notificationEnabled;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // 构造函数
    public Budget() {
        this.id = UUID.randomUUID().toString();
        this.amount = BigDecimal.ZERO;
        this.isActive = true;
        this.notificationEnabled = true;
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    public Budget(String categoryId, BigDecimal amount, BudgetPeriod period, 
                  Date startDate, Date endDate) {
        this();
        this.categoryId = categoryId;
        this.amount = amount;
        this.period = period;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BudgetPeriod getPeriod() {
        return period;
    }

    public void setPeriod(BudgetPeriod period) {
        this.period = period;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public boolean isNotificationEnabled() {
        return notificationEnabled;
    }

    public void setNotificationEnabled(boolean notificationEnabled) {
        this.notificationEnabled = notificationEnabled;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取预算周期的显示名称
     * @param isEnglish 是否为英文
     * @return 显示名称
     */
    public String getPeriodDisplayName(boolean isEnglish) {
        return period != null ? period.getDisplayName(isEnglish) : "";
    }

    /**
     * 检查预算是否在指定日期范围内有效
     * @param date 检查的日期
     * @return true如果有效
     */
    public boolean isValidForDate(Date date) {
        if (!isActive || date == null) {
            return false;
        }
        return (startDate == null || !date.before(startDate)) && 
               (endDate == null || !date.after(endDate));
    }
}
