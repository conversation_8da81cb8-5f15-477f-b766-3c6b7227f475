package com.photo.restore.bookkeeping.ui.settings;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.switchmaterial.SwitchMaterial;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.repository.AccountRepository;
import com.photo.restore.bookkeeping.data.repository.CategoryRepository;
import com.photo.restore.bookkeeping.data.repository.TransactionRepository;
import com.photo.restore.bookkeeping.utils.DataExportUtils;
import com.photo.restore.bookkeeping.utils.LocaleUtils;
import com.photo.restore.bookkeeping.utils.ThemeUtils;
import com.photo.restore.bookkeeping.utils.DataBackupUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 设置Fragment
 * 提供应用设置和配置选项
 */
public class SettingsFragment extends Fragment {

    private SettingsViewModel settingsViewModel;
    private TransactionRepository transactionRepository;
    private AccountRepository accountRepository;
    private CategoryRepository categoryRepository;
    private ExecutorService executor;
    
    // UI组件
    private MaterialToolbar toolbar;
    private LinearLayout layoutCurrency;
    private TextView tvCurrency;
    private LinearLayout layoutTheme;
    private TextView tvTheme;
    private LinearLayout layoutAccountManagement;
    private LinearLayout layoutCategoryManagement;
    private LinearLayout layoutBackup;
    private LinearLayout layoutExport;
    private LinearLayout layoutAbout;
    private TextView tvVersion;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化ViewModel
        settingsViewModel = new ViewModelProvider(this).get(SettingsViewModel.class);

        // 初始化Repository
        transactionRepository = new TransactionRepository(requireActivity().getApplication());
        accountRepository = new AccountRepository(requireActivity().getApplication());
        categoryRepository = new CategoryRepository(requireActivity().getApplication());

        // 初始化线程池
        executor = Executors.newSingleThreadExecutor();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_settings, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupClickListeners();
        observeData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        layoutCurrency = view.findViewById(R.id.layout_currency);
        tvCurrency = view.findViewById(R.id.tv_currency);
        layoutTheme = view.findViewById(R.id.layout_theme);
        tvTheme = view.findViewById(R.id.tv_theme);
        layoutAccountManagement = view.findViewById(R.id.layout_account_management);
        layoutCategoryManagement = view.findViewById(R.id.layout_category_management);
        layoutBackup = view.findViewById(R.id.layout_backup);
        layoutExport = view.findViewById(R.id.layout_export);
        layoutAbout = view.findViewById(R.id.layout_about);
        tvVersion = view.findViewById(R.id.tv_version);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 货币设置
        layoutCurrency.setOnClickListener(v -> {
            showCurrencyDialog();
        });

        // 主题设置
        layoutTheme.setOnClickListener(v -> {
            showThemeDialog();
        });

        // 账户管理
        layoutAccountManagement.setOnClickListener(v -> {
            showAccountManagement();
        });

        // 分类管理
        layoutCategoryManagement.setOnClickListener(v -> {
            showCategoryManagement();
        });

        // 备份
        layoutBackup.setOnClickListener(v -> {
            performBackup();
        });

        // 导出
        layoutExport.setOnClickListener(v -> {
            performExport();
        });

        // 关于
        layoutAbout.setOnClickListener(v -> {
            showAboutDialog();
        });
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        // 观察货币设置
        settingsViewModel.getCurrency().observe(getViewLifecycleOwner(), currency -> {
            tvCurrency.setText(currency);
        });

        // 观察主题设置
        settingsViewModel.getThemeMode().observe(getViewLifecycleOwner(), themeMode -> {
            tvTheme.setText(ThemeUtils.getThemeModeDisplayName(requireContext(), themeMode));
        });

        // 观察版本信息
        settingsViewModel.getVersionName().observe(getViewLifecycleOwner(), version -> {
            tvVersion.setText("v" + version);
        });
    }



    /**
     * 显示货币选择对话框
     */
    private void showCurrencyDialog() {
        String[] currencyCodes = {
            getString(R.string.currency_code_cny),
            getString(R.string.currency_code_usd),
            getString(R.string.currency_code_eur),
            getString(R.string.currency_code_jpy),
            getString(R.string.currency_code_gbp)
        };
        String[] currencies = new String[currencyCodes.length];

        // 生成货币显示名称
        for (int i = 0; i < currencyCodes.length; i++) {
            currencies[i] = LocaleUtils.getCurrencyDisplayName(requireContext(), currencyCodes[i]);
        }

        String currentCurrency = settingsViewModel.getCurrency().getValue();
        int selectedIndex = 0;
        for (int i = 0; i < currencyCodes.length; i++) {
            if (currencyCodes[i].equals(currentCurrency)) {
                selectedIndex = i;
                break;
            }
        }

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.select_currency))
                .setSingleChoiceItems(currencies, selectedIndex, (dialog, which) -> {
                    settingsViewModel.setCurrency(currencyCodes[which]);
                    dialog.dismiss();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示主题选择对话框
     */
    private void showThemeDialog() {
        String[] themeNames = ThemeUtils.getThemeModeDisplayNames(requireContext());
        int[] themeModes = ThemeUtils.getSupportedThemeModes();

        int currentThemeMode = settingsViewModel.getThemeMode().getValue();
        int selectedIndex = 0;
        for (int i = 0; i < themeModes.length; i++) {
            if (themeModes[i] == currentThemeMode) {
                selectedIndex = i;
                break;
            }
        }

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.select_theme))
                .setSingleChoiceItems(themeNames, selectedIndex, (dialog, which) -> {
                    int selectedThemeMode = themeModes[which];
                    if (ThemeUtils.needsActivityRestart(requireContext(), selectedThemeMode)) {
                        settingsViewModel.setThemeMode(selectedThemeMode);
                        requireActivity().recreate(); // 重启Activity应用新主题
                    }
                    dialog.dismiss();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 执行备份
     */
    private void performBackup() {
        if (!DataExportUtils.hasStoragePermission(requireContext())) {
            Toast.makeText(getContext(), getString(R.string.storage_permission_required), Toast.LENGTH_SHORT).show();
            return;
        }

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.data_backup))
                .setMessage(getString(R.string.backup_confirm_message))
                .setPositiveButton(getString(R.string.ok), (dialog, which) -> {
                    performActualBackup();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 执行实际的备份操作
     */
    private void performActualBackup() {
        DataBackupUtils.performBackup(requireContext(), new DataBackupUtils.BackupCallback() {
            @Override
            public void onSuccess(String backupFilePath) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(),
                        getString(R.string.backup_success, backupFilePath),
                        Toast.LENGTH_LONG).show();
                });
            }

            @Override
            public void onError(String errorMessage) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * 执行导出
     */
    private void performExport() {
        if (!DataExportUtils.hasStoragePermission(requireContext())) {
            Toast.makeText(getContext(), getString(R.string.storage_permission_required), Toast.LENGTH_SHORT).show();
            return;
        }

        String[] exportOptions = {
            getString(R.string.export_transactions),
            getString(R.string.export_accounts),
            getString(R.string.export_categories),
            getString(R.string.export_all_data)
        };

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.select_export_content))
                .setItems(exportOptions, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            exportTransactions();
                            break;
                        case 1:
                            exportAccounts();
                            break;
                        case 2:
                            exportCategories();
                            break;
                        case 3:
                            exportAllData();
                            break;
                    }
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示账户管理
     */
    private void showAccountManagement() {
        NavController navController = Navigation.findNavController(requireView());
        navController.navigate(R.id.action_settings_to_account_management);
    }

    /**
     * 显示分类管理
     */
    private void showCategoryManagement() {
        NavController navController = Navigation.findNavController(requireView());
        navController.navigate(R.id.action_settings_to_category_management);
    }

    /**
     * 显示关于对话框
     */
    private void showAboutDialog() {
        String versionName = settingsViewModel.getVersionName().getValue();
        String aboutMessage = getString(R.string.about_message, versionName);

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.about_money))
                .setMessage(aboutMessage)
                .setPositiveButton(getString(R.string.ok), null)
                .show();
    }

    /**
     * 导出交易记录
     */
    private void exportTransactions() {
        try {
            transactionRepository.getAllTransactionsWithDetailsLiveData().observe(requireActivity(), transactions -> {
                if (transactions != null && !transactions.isEmpty()) {
                    // 在后台线程中执行导出操作
                    executor.execute(() -> {
                        try {
                            DataExportUtils.exportTransactionsToCSV(requireContext(), transactions);
                        } catch (Exception e) {
                            requireActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
                            });
                        }
                    });
                } else {
                    Toast.makeText(getContext(), getString(R.string.no_transactions_to_export), Toast.LENGTH_SHORT).show();
                }
            });
        } catch (Exception e) {
            Toast.makeText(getContext(), getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 导出账户信息
     */
    private void exportAccounts() {
        executor.execute(() -> {
            try {
                accountRepository.getAllActiveAccountsLiveData().observe(requireActivity(), accounts -> {
                    if (accounts != null && !accounts.isEmpty()) {
                        DataExportUtils.exportAccountsToCSV(requireContext(), accounts);
                    } else {
                        Toast.makeText(getContext(), getString(R.string.no_accounts_to_export), Toast.LENGTH_SHORT).show();
                    }
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * 导出分类信息
     */
    private void exportCategories() {
        executor.execute(() -> {
            try {
                categoryRepository.getAllActiveCategoriesLiveData().observe(requireActivity(), categories -> {
                    if (categories != null && !categories.isEmpty()) {
                        DataExportUtils.exportCategoriesToCSV(requireContext(), categories);
                    } else {
                        Toast.makeText(getContext(), getString(R.string.no_categories_to_export), Toast.LENGTH_SHORT).show();
                    }
                });
            } catch (Exception e) {
                requireActivity().runOnUiThread(() -> {
                    Toast.makeText(getContext(), getString(R.string.export_failed, e.getMessage()), Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * 导出全部数据
     */
    private void exportAllData() {
        Toast.makeText(getContext(), getString(R.string.exporting_all_data), Toast.LENGTH_SHORT).show();
        exportTransactions();
        exportAccounts();
        exportCategories();
    }



    @Override
    public void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
