package com.photo.restore.bookkeeping.data.repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.photo.restore.bookkeeping.R;

import com.photo.restore.bookkeeping.data.dao.AccountDao;
import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.enums.AccountType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 账户仓库类
 */
public class AccountRepository {

    private final AccountDao accountDao;
    private final ExecutorService executor;
    private final LiveData<List<Account>> allActiveAccountsLiveData;
    private final LiveData<BigDecimal> totalAssetsLiveData;
    private final Application application;

    public AccountRepository(Application application) {
        this.application = application;
        BookkeepingDatabase database = BookkeepingDatabase.getInstance(application);
        accountDao = database.accountDao();
        executor = Executors.newFixedThreadPool(2);
        allActiveAccountsLiveData = accountDao.getAllActiveAccountsLiveData();
        totalAssetsLiveData = accountDao.getTotalAssetsLiveData();
    }

    /**
     * 获取所有活跃账户（LiveData）
     * @return 账户列表LiveData
     */
    public LiveData<List<Account>> getAllActiveAccountsLiveData() {
        return allActiveAccountsLiveData;
    }

    /**
     * 获取所有账户（包括非活跃）（LiveData）
     * @return 账户列表LiveData
     */
    public LiveData<List<Account>> getAllAccountsLiveData() {
        return accountDao.getAllAccountsLiveData();
    }

    /**
     * 获取所有活跃账户（同步）
     * @return 账户列表
     */
    public List<Account> getAllActiveAccounts() {
        return accountDao.getAllActiveAccounts();
    }

    /**
     * 根据ID获取账户
     * @param accountId 账户ID
     * @return 账户LiveData
     */
    public LiveData<Account> getAccountByIdLiveData(String accountId) {
        return accountDao.getAccountByIdLiveData(accountId);
    }

    /**
     * 根据ID获取账户（同步）
     * @param accountId 账户ID
     * @return 账户
     */
    public Account getAccountById(String accountId) {
        return accountDao.getAccountById(accountId);
    }

    /**
     * 根据类型获取账户
     * @param type 账户类型
     * @return 账户列表
     */
    public List<Account> getAccountsByType(AccountType type) {
        return accountDao.getAccountsByType(type);
    }

    /**
     * 根据货币获取账户
     * @param currency 货币代码
     * @return 账户列表
     */
    public List<Account> getAccountsByCurrency(String currency) {
        return accountDao.getAccountsByCurrency(currency);
    }

    /**
     * 获取总资产（LiveData）
     * @return 总资产LiveData
     */
    public LiveData<BigDecimal> getTotalAssetsLiveData() {
        return totalAssetsLiveData;
    }

    /**
     * 获取总资产（同步）
     * @return 总资产
     */
    public BigDecimal getTotalAssets() {
        return accountDao.getTotalAssets();
    }

    /**
     * 根据货币获取总资产
     * @param currency 货币代码
     * @return 总资产
     */
    public BigDecimal getTotalAssetsByCurrency(String currency) {
        return accountDao.getTotalAssetsByCurrency(currency);
    }

    /**
     * 插入账户
     * @param account 账户
     */
    public void insertAccount(Account account) {
        executor.execute(() -> accountDao.insertAccount(account));
    }

    /**
     * 批量插入账户
     * @param accounts 账户列表
     */
    public void insertAccounts(List<Account> accounts) {
        executor.execute(() -> accountDao.insertAccounts(accounts));
    }

    /**
     * 更新账户
     * @param account 账户
     */
    public void updateAccount(Account account) {
        executor.execute(() -> {
            account.setUpdatedAt(new Date());
            accountDao.updateAccount(account);
        });
    }

    /**
     * 更新账户余额
     * @param accountId 账户ID
     * @param balance 新余额
     */
    public void updateAccountBalance(String accountId, BigDecimal balance) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            accountDao.updateAccountBalance(accountId, balance, currentTime);
        });
    }

    /**
     * 增加账户余额
     * @param accountId 账户ID
     * @param amount 增加金额
     */
    public void increaseAccountBalance(String accountId, BigDecimal amount) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            accountDao.increaseAccountBalance(accountId, amount, currentTime);
        });
    }

    /**
     * 减少账户余额
     * @param accountId 账户ID
     * @param amount 减少金额
     */
    public void decreaseAccountBalance(String accountId, BigDecimal amount) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            accountDao.decreaseAccountBalance(accountId, amount, currentTime);
        });
    }

    /**
     * 软删除账户
     * @param accountId 账户ID
     */
    public void softDeleteAccount(String accountId) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            accountDao.softDeleteAccount(accountId, currentTime);
        });
    }

    /**
     * 删除账户
     * @param account 账户
     */
    public void deleteAccount(Account account) {
        executor.execute(() -> accountDao.deleteAccount(account));
    }

    /**
     * 获取账户数量
     * @return 账户数量
     */
    public int getAccountCount() {
        return accountDao.getAccountCount();
    }

    /**
     * 创建默认账户（仅在没有账户时创建基础账户）
     */
    public void createDefaultAccounts() {
        executor.execute(() -> {
            if (accountDao.getAccountCount() == 0) {
                // 创建默认现金账户
                Account cashAccount = new Account();
                cashAccount.setName(application.getString(R.string.default_cash_account));
                cashAccount.setType(AccountType.CASH);
                cashAccount.setCurrency("USD");
                cashAccount.setCurrentBalance(BigDecimal.ZERO);
                cashAccount.setInitialBalance(BigDecimal.ZERO);
                accountDao.insertAccount(cashAccount);
            }
        });
    }

    /**
     * 获取总资产（LiveData）
     * @return 总资产LiveData
     */
    public LiveData<BigDecimal> getTotalBalanceLiveData() {
        return accountDao.getTotalAssetsLiveData();
    }

    /**
     * 获取总资产（同步）
     * @return 总资产
     */
    public BigDecimal getTotalBalance() {
        return accountDao.getTotalAssets();
    }

    /**
     * 根据货币获取总资产
     * @param currency 货币代码
     * @return 总资产
     */
    public BigDecimal getTotalBalanceByCurrency(String currency) {
        return accountDao.getTotalAssetsByCurrency(currency);
    }

    /**
     * 处理转账操作
     * @param fromAccountId 转出账户ID
     * @param toAccountId 转入账户ID
     * @param amount 转账金额
     * @param fee 手续费
     */
    public void processTransfer(String fromAccountId, String toAccountId, BigDecimal amount, BigDecimal fee) {
        executor.execute(() -> {
            long currentTime = System.currentTimeMillis();
            // 从转出账户扣除金额和手续费
            BigDecimal totalDeduction = amount.add(fee != null ? fee : BigDecimal.ZERO);
            accountDao.decreaseAccountBalance(fromAccountId, totalDeduction, currentTime);
            // 向转入账户增加金额
            accountDao.increaseAccountBalance(toAccountId, amount, currentTime);
        });
    }

    /**
     * 根据名称查找账户
     * @param name 账户名称
     * @return 账户对象，如果未找到则返回null
     */
    public Account getAccountByName(String name) {
        return accountDao.getAccountByName(name);
    }
}
