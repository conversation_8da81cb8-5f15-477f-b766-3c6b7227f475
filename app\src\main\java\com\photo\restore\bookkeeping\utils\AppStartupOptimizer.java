package com.photo.restore.bookkeeping.utils;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.photo.restore.bookkeeping.data.database.BookkeepingDatabase;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 应用启动优化工具类
 */
public class AppStartupOptimizer {
    
    private static final String TAG = "AppStartupOptimizer";
    private static final String PREFS_NAME = "startup_optimizer";
    private static final String KEY_FIRST_LAUNCH = "first_launch";
    private static final String KEY_LAST_OPTIMIZATION = "last_optimization";
    
    private static final long OPTIMIZATION_INTERVAL = 24 * 60 * 60 * 1000; // 24小时
    
    private static ExecutorService backgroundExecutor;
    private static Handler mainHandler;
    
    /**
     * 初始化启动优化器
     */
    public static void initialize(Application application) {
        PerformanceUtils.startTimer("AppStartupOptimizer.initialize");
        
        backgroundExecutor = Executors.newSingleThreadExecutor();
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 异步执行启动优化任务
        backgroundExecutor.execute(() -> {
            try {
                performStartupOptimizations(application);
            } catch (Exception e) {
                Log.e(TAG, "Error during startup optimization", e);
            }
        });
        
        PerformanceUtils.endTimer("AppStartupOptimizer.initialize");
    }
    
    /**
     * 执行启动优化任务
     */
    private static void performStartupOptimizations(Context context) {
        PerformanceUtils.startTimer("StartupOptimizations");
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean isFirstLaunch = prefs.getBoolean(KEY_FIRST_LAUNCH, true);
        long lastOptimization = prefs.getLong(KEY_LAST_OPTIMIZATION, 0);
        long currentTime = System.currentTimeMillis();
        
        // 检查是否需要执行优化
        boolean shouldOptimize = isFirstLaunch || 
                                (currentTime - lastOptimization > OPTIMIZATION_INTERVAL);
        
        if (shouldOptimize) {
            Log.d(TAG, "Performing startup optimizations...");
            
            // 1. 预热数据库连接
            warmupDatabase(context);
            
            // 2. 预加载关键数据
            preloadCriticalData(context);
            
            // 3. 清理过期缓存
            cleanupExpiredCaches(context);
            
            // 4. 优化SharedPreferences
            optimizeSharedPreferences(context);
            
            // 更新优化时间戳
            prefs.edit()
                    .putBoolean(KEY_FIRST_LAUNCH, false)
                    .putLong(KEY_LAST_OPTIMIZATION, currentTime)
                    .apply();
            
            Log.d(TAG, "Startup optimizations completed");
        }
        
        PerformanceUtils.endTimer("StartupOptimizations");
    }
    
    /**
     * 预热数据库连接
     */
    private static void warmupDatabase(Context context) {
        PerformanceUtils.measureDatabaseOperation("DatabaseWarmup", () -> {
            try {
                BookkeepingDatabase database = BookkeepingDatabase.getInstance(context);
                // 执行一个简单的查询来预热连接
                database.accountDao().getAllActiveAccounts();
                Log.d(TAG, "Database warmed up successfully");
                return null;
            } catch (Exception e) {
                Log.w(TAG, "Database warmup failed", e);
                return null;
            }
        });
    }
    
    /**
     * 预加载关键数据
     */
    private static void preloadCriticalData(Context context) {
        PerformanceUtils.measureDatabaseOperation("PreloadCriticalData", () -> {
            try {
                BookkeepingDatabase database = BookkeepingDatabase.getInstance(context);
                
                // 预加载账户数据
                database.accountDao().getAllActiveAccounts();
                
                // 预加载分类数据
                database.categoryDao().getAllActiveCategories();
                
                // 预加载最近的交易记录
                database.transactionDao().getRecentTransactions(10);
                
                Log.d(TAG, "Critical data preloaded successfully");
                return null;
            } catch (Exception e) {
                Log.w(TAG, "Critical data preload failed", e);
                return null;
            }
        });
    }
    
    /**
     * 清理过期缓存
     */
    private static void cleanupExpiredCaches(Context context) {
        PerformanceUtils.measureTime("CleanupExpiredCaches", () -> {
            try {
                // 清理应用缓存目录中的过期文件
                cleanupCacheDirectory(context);
                
                // 清理SharedPreferences中的过期数据
                cleanupExpiredPreferences(context);
                
                Log.d(TAG, "Expired caches cleaned up successfully");
            } catch (Exception e) {
                Log.w(TAG, "Cache cleanup failed", e);
            }
        });
    }
    
    /**
     * 清理缓存目录
     */
    private static void cleanupCacheDirectory(Context context) {
        try {
            java.io.File cacheDir = context.getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                java.io.File[] files = cacheDir.listFiles();
                if (files != null) {
                    long currentTime = System.currentTimeMillis();
                    long maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
                    
                    for (java.io.File file : files) {
                        if (currentTime - file.lastModified() > maxAge) {
                            if (file.delete()) {
                                Log.d(TAG, "Deleted expired cache file: " + file.getName());
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to cleanup cache directory", e);
        }
    }
    
    /**
     * 清理过期的SharedPreferences数据
     */
    private static void cleanupExpiredPreferences(Context context) {
        try {
            // 这里可以清理一些临时的或过期的preference数据
            // 例如：临时设置、缓存的网络数据等
            Log.d(TAG, "Expired preferences cleaned up");
        } catch (Exception e) {
            Log.w(TAG, "Failed to cleanup expired preferences", e);
        }
    }
    
    /**
     * 优化SharedPreferences
     */
    private static void optimizeSharedPreferences(Context context) {
        PerformanceUtils.measureTime("OptimizeSharedPreferences", () -> {
            try {
                // 合并多个小的preference文件（如果有的话）
                // 清理不再使用的preference键
                // 这里可以根据具体需求实现
                
                Log.d(TAG, "SharedPreferences optimized");
            } catch (Exception e) {
                Log.w(TAG, "SharedPreferences optimization failed", e);
            }
        });
    }
    
    /**
     * 延迟执行非关键任务
     */
    public static void scheduleNonCriticalTasks(Context context) {
        if (backgroundExecutor == null) {
            return;
        }
        
        // 延迟3秒执行非关键任务，避免影响启动性能
        mainHandler.postDelayed(() -> {
            backgroundExecutor.execute(() -> {
                try {
                    // 执行非关键的后台任务
                    performNonCriticalTasks(context);
                } catch (Exception e) {
                    Log.e(TAG, "Error during non-critical tasks", e);
                }
            });
        }, 3000);
    }
    
    /**
     * 执行非关键任务
     */
    private static void performNonCriticalTasks(Context context) {
        Log.d(TAG, "Performing non-critical tasks...");
        
        // 1. 检查和更新汇率数据
        updateExchangeRatesIfNeeded(context);
        
        // 2. 清理旧的备份文件
        cleanupOldBackupFiles(context);
        
        // 3. 预计算统计数据
        precomputeStatistics(context);
        
        Log.d(TAG, "Non-critical tasks completed");
    }
    
    /**
     * 更新汇率数据（如果需要）
     */
    private static void updateExchangeRatesIfNeeded(Context context) {
        // 这里可以实现汇率更新逻辑
        Log.d(TAG, "Exchange rates update check completed");
    }
    
    /**
     * 清理旧的备份文件
     */
    private static void cleanupOldBackupFiles(Context context) {
        try {
            // 清理超过30天的备份文件
            Log.d(TAG, "Old backup files cleanup completed");
        } catch (Exception e) {
            Log.w(TAG, "Failed to cleanup old backup files", e);
        }
    }
    
    /**
     * 预计算统计数据
     */
    private static void precomputeStatistics(Context context) {
        try {
            // 预计算一些常用的统计数据，提高后续查询性能
            Log.d(TAG, "Statistics precomputation completed");
        } catch (Exception e) {
            Log.w(TAG, "Failed to precompute statistics", e);
        }
    }
    
    /**
     * 关闭启动优化器
     */
    public static void shutdown() {
        if (backgroundExecutor != null && !backgroundExecutor.isShutdown()) {
            backgroundExecutor.shutdown();
        }
    }
    
    /**
     * 获取启动优化统计信息
     */
    public static String getOptimizationStats(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        boolean isFirstLaunch = prefs.getBoolean(KEY_FIRST_LAUNCH, true);
        long lastOptimization = prefs.getLong(KEY_LAST_OPTIMIZATION, 0);
        
        return String.format("First launch: %s, Last optimization: %s", 
                isFirstLaunch, 
                lastOptimization > 0 ? new java.util.Date(lastOptimization).toString() : "Never");
    }
}
