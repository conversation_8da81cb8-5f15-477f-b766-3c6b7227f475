package com.photo.restore.bookkeeping.utils;

import android.content.Context;
import android.util.Log;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 代码质量和数据验证工具类
 */
public class CodeQualityUtils {
    
    private static final String TAG = "CodeQualityUtils";
    
    /**
     * 验证字符串是否为空或null
     */
    public static boolean isStringValid(String str) {
        return str != null && !str.trim().isEmpty();
    }
    
    /**
     * 验证BigDecimal是否有效（非null且大于等于0）
     */
    public static boolean isBigDecimalValid(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) >= 0;
    }
    
    /**
     * 验证BigDecimal是否为正数
     */
    public static boolean isBigDecimalPositive(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 验证日期是否有效
     */
    public static boolean isDateValid(Date date) {
        return date != null && date.getTime() > 0;
    }
    
    /**
     * 验证列表是否非空
     */
    public static boolean isListValid(List<?> list) {
        return list != null && !list.isEmpty();
    }
    
    /**
     * 安全地获取字符串，避免null
     */
    public static String safeString(String str) {
        return str != null ? str.trim() : "";
    }
    
    /**
     * 安全地获取BigDecimal，避免null
     */
    public static BigDecimal safeBigDecimal(BigDecimal value) {
        return value != null ? value : BigDecimal.ZERO;
    }
    
    /**
     * 安全地获取Date，避免null
     */
    public static Date safeDate(Date date) {
        return date != null ? date : new Date();
    }
    
    /**
     * 验证金额格式
     */
    public static boolean isAmountValid(String amountStr) {
        if (!isStringValid(amountStr)) {
            return false;
        }
        
        try {
            BigDecimal amount = new BigDecimal(amountStr);
            return amount.compareTo(BigDecimal.ZERO) > 0 && 
                   amount.compareTo(new BigDecimal("*********.99")) <= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 验证账户ID格式
     */
    public static boolean isAccountIdValid(String accountId) {
        return isStringValid(accountId) && accountId.length() >= 10; // UUID长度检查
    }
    
    /**
     * 验证分类ID格式
     */
    public static boolean isCategoryIdValid(String categoryId) {
        return isStringValid(categoryId) && categoryId.length() >= 10; // UUID长度检查
    }
    
    /**
     * 验证货币代码格式
     */
    public static boolean isCurrencyCodeValid(String currencyCode) {
        return isStringValid(currencyCode) && 
               currencyCode.length() == 3 && 
               currencyCode.matches("[A-Z]{3}");
    }
    
    /**
     * 验证语言代码格式
     */
    public static boolean isLanguageCodeValid(String languageCode) {
        return isStringValid(languageCode) && 
               (languageCode.equals("zh-CN") || languageCode.equals("en-US"));
    }
    
    /**
     * 验证颜色代码格式
     */
    public static boolean isColorCodeValid(String colorCode) {
        return isStringValid(colorCode) && 
               colorCode.matches("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$");
    }
    
    /**
     * 验证图标名称格式
     */
    public static boolean isIconNameValid(String iconName) {
        return isStringValid(iconName) && 
               iconName.matches("^[a-z_]+$") && 
               iconName.length() <= 50;
    }
    
    /**
     * 格式化金额显示
     */
    public static String formatAmount(BigDecimal amount, String currencyCode) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        if (!isCurrencyCodeValid(currencyCode)) {
            currencyCode = "CNY";
        }
        
        // 根据货币代码格式化显示
        switch (currencyCode) {
            case "CNY":
                return "¥" + amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            case "USD":
                return "$" + amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            case "EUR":
                return "€" + amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
            default:
                return currencyCode + " " + amount.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }
    }
    
    /**
     * 验证备份文件路径
     */
    public static boolean isBackupFilePathValid(String filePath) {
        return isStringValid(filePath) && 
               filePath.endsWith(".json") && 
               filePath.contains("money_backup_");
    }
    
    /**
     * 验证导出文件路径
     */
    public static boolean isExportFilePathValid(String filePath) {
        return isStringValid(filePath) && 
               filePath.endsWith(".csv") && 
               filePath.contains("money_export_");
    }
    
    /**
     * 记录数据验证错误
     */
    public static void logValidationError(String field, String value, String reason) {
        Log.w(TAG, String.format("Validation failed for field '%s' with value '%s': %s", 
                field, value, reason));
    }
    
    /**
     * 记录数据验证成功
     */
    public static void logValidationSuccess(String field, String value) {
        Log.d(TAG, String.format("Validation passed for field '%s' with value '%s'", 
                field, value));
    }
    
    /**
     * 验证交易数据完整性
     */
    public static boolean validateTransactionData(String accountId, String categoryId, 
                                                 BigDecimal amount, Date date, String description) {
        boolean isValid = true;
        
        if (!isAccountIdValid(accountId)) {
            logValidationError("accountId", accountId, "Invalid account ID format");
            isValid = false;
        }
        
        if (!isCategoryIdValid(categoryId)) {
            logValidationError("categoryId", categoryId, "Invalid category ID format");
            isValid = false;
        }
        
        if (!isBigDecimalPositive(amount)) {
            logValidationError("amount", amount != null ? amount.toString() : "null", 
                             "Amount must be positive");
            isValid = false;
        }
        
        if (!isDateValid(date)) {
            logValidationError("date", date != null ? date.toString() : "null", 
                             "Invalid date");
            isValid = false;
        }
        
        if (!isStringValid(description)) {
            logValidationError("description", description, "Description cannot be empty");
            isValid = false;
        }
        
        if (isValid) {
            logValidationSuccess("transaction", "All fields valid");
        }
        
        return isValid;
    }
    
    /**
     * 验证账户数据完整性
     */
    public static boolean validateAccountData(String name, String currency, 
                                            BigDecimal initialBalance, String iconName, String color) {
        boolean isValid = true;
        
        if (!isStringValid(name)) {
            logValidationError("name", name, "Account name cannot be empty");
            isValid = false;
        }
        
        if (!isCurrencyCodeValid(currency)) {
            logValidationError("currency", currency, "Invalid currency code");
            isValid = false;
        }
        
        if (!isBigDecimalValid(initialBalance)) {
            logValidationError("initialBalance", 
                             initialBalance != null ? initialBalance.toString() : "null", 
                             "Initial balance must be non-negative");
            isValid = false;
        }
        
        if (!isIconNameValid(iconName)) {
            logValidationError("iconName", iconName, "Invalid icon name format");
            isValid = false;
        }
        
        if (!isColorCodeValid(color)) {
            logValidationError("color", color, "Invalid color code format");
            isValid = false;
        }
        
        if (isValid) {
            logValidationSuccess("account", "All fields valid");
        }
        
        return isValid;
    }
    
    /**
     * 验证分类数据完整性
     */
    public static boolean validateCategoryData(String name, String iconName, String color) {
        boolean isValid = true;
        
        if (!isStringValid(name)) {
            logValidationError("name", name, "Category name cannot be empty");
            isValid = false;
        }
        
        if (!isIconNameValid(iconName)) {
            logValidationError("iconName", iconName, "Invalid icon name format");
            isValid = false;
        }
        
        if (!isColorCodeValid(color)) {
            logValidationError("color", color, "Invalid color code format");
            isValid = false;
        }
        
        if (isValid) {
            logValidationSuccess("category", "All fields valid");
        }
        
        return isValid;
    }
    
    /**
     * 清理和标准化输入数据
     */
    public static class DataCleaner {
        
        /**
         * 清理字符串输入
         */
        public static String cleanString(String input) {
            if (input == null) {
                return "";
            }
            return input.trim().replaceAll("\\s+", " ");
        }
        
        /**
         * 清理金额输入
         */
        public static BigDecimal cleanAmount(String amountStr) {
            if (!isStringValid(amountStr)) {
                return BigDecimal.ZERO;
            }
            
            try {
                // 移除货币符号和空格
                String cleaned = amountStr.replaceAll("[¥$€,\\s]", "");
                BigDecimal amount = new BigDecimal(cleaned);
                return amount.setScale(2, BigDecimal.ROUND_HALF_UP);
            } catch (NumberFormatException e) {
                Log.w(TAG, "Failed to clean amount: " + amountStr, e);
                return BigDecimal.ZERO;
            }
        }
        
        /**
         * 清理颜色代码
         */
        public static String cleanColorCode(String colorCode) {
            if (!isStringValid(colorCode)) {
                return "#FF6B6B"; // 默认颜色
            }
            
            String cleaned = colorCode.trim().toUpperCase();
            if (!cleaned.startsWith("#")) {
                cleaned = "#" + cleaned;
            }
            
            return isColorCodeValid(cleaned) ? cleaned : "#FF6B6B";
        }
    }
}
