package com.photo.restore.bookkeeping.ui.adapter;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Category;

import java.util.List;

/**
 * 分类管理适配器
 */
public class CategoryManagementAdapter extends RecyclerView.Adapter<CategoryManagementAdapter.CategoryViewHolder> {

    private List<Category> categories;
    private final OnCategoryActionListener listener;

    /**
     * 分类操作监听器接口
     */
    public interface OnCategoryActionListener {
        void onEditCategory(Category category);
        void onDeleteCategory(Category category);
        void onToggleCategoryStatus(Category category);
    }

    public CategoryManagementAdapter(List<Category> categories, OnCategoryActionListener listener) {
        this.categories = categories;
        this.listener = listener;
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_category_management, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        Category category = categories.get(position);
        Context context = holder.itemView.getContext();
        
        // 设置分类名称
        holder.tvCategoryName.setText(category.getName());
        
        // 设置分类类型
        String typeText = category.getType().getDisplayName(context);
        holder.tvCategoryType.setText(typeText);
        
        // 设置图标名称
        holder.tvIconName.setText(category.getIconName());
        
        // 设置颜色指示器
        try {
            int color = Color.parseColor(category.getColor());
            GradientDrawable drawable = new GradientDrawable();
            drawable.setShape(GradientDrawable.OVAL);
            drawable.setColor(color);
            holder.viewColorIndicator.setBackground(drawable);
        } catch (IllegalArgumentException e) {
            // 如果颜色格式不正确，使用默认颜色
            holder.viewColorIndicator.setBackgroundColor(ContextCompat.getColor(context, R.color.primary));
        }
        
        // 设置系统默认标识
        if (category.isSystemDefault()) {
            holder.tvSystemDefault.setVisibility(View.VISIBLE);
            holder.btnDelete.setVisibility(View.GONE); // 完全隐藏删除按钮
        } else {
            holder.tvSystemDefault.setVisibility(View.GONE);
            holder.btnDelete.setVisibility(View.VISIBLE); // 显示删除按钮
            holder.btnDelete.setEnabled(true);
            holder.btnDelete.setAlpha(1.0f);
        }
        
        // 设置分类状态
        // 先清除监听器，避免setChecked触发监听器
        holder.switchActive.setOnCheckedChangeListener(null);
        holder.switchActive.setChecked(category.isActive());
        // 再设置监听器
        holder.switchActive.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (listener != null) {
                listener.onToggleCategoryStatus(category);
            }
        });
        
        // 设置编辑按钮
        holder.btnEdit.setOnClickListener(v -> {
            if (listener != null) {
                listener.onEditCategory(category);
            }
        });
        
        // 设置删除按钮
        holder.btnDelete.setOnClickListener(v -> {
            if (listener != null && !category.isSystemDefault()) {
                listener.onDeleteCategory(category);
            }
        });
        
        // 设置项目点击
        holder.itemView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onEditCategory(category);
            }
        });
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    /**
     * 更新分类列表
     */
    public void updateCategories(List<Category> newCategories) {
        this.categories = newCategories;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    static class CategoryViewHolder extends RecyclerView.ViewHolder {
        TextView tvCategoryName;
        TextView tvCategoryType;
        TextView tvIconName;
        TextView tvSystemDefault;
        View viewColorIndicator;
        Switch switchActive;
        ImageButton btnEdit;
        ImageButton btnDelete;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            tvCategoryType = itemView.findViewById(R.id.tv_category_type);
            tvIconName = itemView.findViewById(R.id.tv_icon_name);
            tvSystemDefault = itemView.findViewById(R.id.tv_system_default);
            viewColorIndicator = itemView.findViewById(R.id.view_color_indicator);
            switchActive = itemView.findViewById(R.id.switch_active);
            btnEdit = itemView.findViewById(R.id.btn_edit);
            btnDelete = itemView.findViewById(R.id.btn_delete);
        }
    }
}
