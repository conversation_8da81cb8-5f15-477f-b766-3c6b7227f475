package com.photo.restore.bookkeeping.ui.utils;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.EditText;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * 金额输入格式化监听器
 * 自动格式化金额输入，限制小数位数
 */
public class MoneyTextWatcher implements TextWatcher {

    private final EditText editText;
    private final DecimalFormat decimalFormat;
    private boolean isFormatting = false;

    public MoneyTextWatcher(EditText editText) {
        this.editText = editText;
        this.decimalFormat = new DecimalFormat("#,##0.00");
        this.decimalFormat.setMaximumFractionDigits(2);
        this.decimalFormat.setMinimumFractionDigits(0);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {
        // 不需要处理
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        // 不需要处理
    }

    @Override
    public void afterTextChanged(Editable s) {
        if (isFormatting) {
            return;
        }

        isFormatting = true;

        String input = s.toString();
        
        // 移除所有非数字和小数点的字符
        String cleanInput = input.replaceAll("[^\\d.]", "");
        
        // 确保只有一个小数点
        int dotIndex = cleanInput.indexOf('.');
        if (dotIndex != -1) {
            String beforeDot = cleanInput.substring(0, dotIndex);
            String afterDot = cleanInput.substring(dotIndex + 1);
            
            // 移除后续的小数点
            afterDot = afterDot.replaceAll("\\.", "");
            
            // 限制小数位数为2位
            if (afterDot.length() > 2) {
                afterDot = afterDot.substring(0, 2);
            }
            
            cleanInput = beforeDot + "." + afterDot;
        }

        // 如果输入为空或只有小数点，不做处理
        if (cleanInput.isEmpty() || cleanInput.equals(".")) {
            isFormatting = false;
            return;
        }

        try {
            // 验证是否为有效数字
            BigDecimal value = new BigDecimal(cleanInput);
            
            // 限制最大值（可选）
            BigDecimal maxValue = new BigDecimal("999999999.99");
            if (value.compareTo(maxValue) > 0) {
                cleanInput = maxValue.toString();
            }

            // 更新文本
            if (!cleanInput.equals(input)) {
                editText.setText(cleanInput);
                editText.setSelection(cleanInput.length());
            }
            
        } catch (NumberFormatException e) {
            // 如果不是有效数字，恢复到之前的状态
            String previousText = input.substring(0, Math.max(0, input.length() - 1));
            editText.setText(previousText);
            editText.setSelection(previousText.length());
        }

        isFormatting = false;
    }

    /**
     * 获取格式化后的金额值
     */
    public BigDecimal getMoneyValue() {
        String text = editText.getText().toString().trim();
        if (text.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        try {
            return new BigDecimal(text);
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 设置金额值
     */
    public void setMoneyValue(BigDecimal value) {
        if (value == null) {
            editText.setText("");
            return;
        }
        
        isFormatting = true;
        editText.setText(value.toString());
        editText.setSelection(editText.getText().length());
        isFormatting = false;
    }
}
