package com.photo.restore.bookkeeping.ui.add;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AutoCompleteTextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.button.MaterialButtonToggleGroup;
import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.entity.Category;
import com.photo.restore.bookkeeping.data.enums.TransactionType;
import com.photo.restore.bookkeeping.ui.adapter.AccountSpinnerAdapter;
import com.photo.restore.bookkeeping.ui.adapter.CategorySpinnerAdapter;
import com.photo.restore.bookkeeping.ui.utils.MoneyTextWatcher;
import com.photo.restore.bookkeeping.ui.utils.NumberKeypadHelper;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 添加交易Fragment
 * 用于添加新的收入或支出记录
 */
public class AddTransactionFragment extends Fragment {

    private AddTransactionViewModel addTransactionViewModel;
    
    // UI组件
    private MaterialToolbar toolbar;
    private MaterialButtonToggleGroup toggleTransactionType;
    private MaterialButton btnExpense;
    private MaterialButton btnIncome;
    private TextInputLayout tilAmount;
    private TextInputEditText etAmount;
    private TextInputLayout tilCategory;
    private AutoCompleteTextView etCategory;
    private TextInputLayout tilAccount;
    private AutoCompleteTextView etAccount;
    private TextInputLayout tilDescription;
    private TextInputEditText etDescription;
    private TextInputLayout tilNote;
    private TextInputEditText etNote;
    private MaterialButton btnSave;

    // 适配器
    private CategorySpinnerAdapter categoryAdapter;
    private AccountSpinnerAdapter accountAdapter;

    // 选中的分类和账户
    private Category selectedCategory;
    private Account selectedAccount;

    // 金额输入监听器
    private MoneyTextWatcher moneyTextWatcher;

    // 数字键盘辅助类
    private NumberKeypadHelper keypadHelper;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化ViewModel
        addTransactionViewModel = new ViewModelProvider(this).get(AddTransactionViewModel.class);
        
        // 检查传入的参数
        Bundle arguments = getArguments();
        if (arguments != null) {
            String transactionType = arguments.getString("transaction_type");
            if (transactionType != null) {
                TransactionType type = TransactionType.valueOf(transactionType);
                addTransactionViewModel.setTransactionType(type);
            }
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_add_transaction, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupClickListeners();
        observeData();
        setupAdapters(view);
        setupInitialState();
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        toggleTransactionType = view.findViewById(R.id.toggle_transaction_type);
        btnExpense = view.findViewById(R.id.btn_expense);
        btnIncome = view.findViewById(R.id.btn_income);
        tilAmount = view.findViewById(R.id.til_amount);
        etAmount = view.findViewById(R.id.et_amount);
        tilCategory = view.findViewById(R.id.til_category);
        etCategory = view.findViewById(R.id.et_category);
        tilAccount = view.findViewById(R.id.til_account);
        etAccount = view.findViewById(R.id.et_account);
        tilDescription = view.findViewById(R.id.til_description);
        etDescription = view.findViewById(R.id.et_description);
        tilNote = view.findViewById(R.id.til_note);
        etNote = view.findViewById(R.id.et_note);
        btnSave = view.findViewById(R.id.btn_save);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 交易类型切换
        toggleTransactionType.addOnButtonCheckedListener((group, checkedId, isChecked) -> {
            if (isChecked) {
                if (checkedId == R.id.btn_income) {
                    addTransactionViewModel.setTransactionType(TransactionType.INCOME);
                } else if (checkedId == R.id.btn_expense) {
                    addTransactionViewModel.setTransactionType(TransactionType.EXPENSE);
                }
            }
        });

        // 保存按钮
        btnSave.setOnClickListener(v -> {
            saveTransaction();
        });
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        // 观察交易类型变化
        addTransactionViewModel.getTransactionType().observe(getViewLifecycleOwner(), type -> {
            if (type == TransactionType.INCOME) {
                toggleTransactionType.check(R.id.btn_income);
            } else {
                toggleTransactionType.check(R.id.btn_expense);
            }

            // 更新分类列表
            updateCategoryList(type);
        });

        // 观察保存结果
        addTransactionViewModel.getSaveResult().observe(getViewLifecycleOwner(), result -> {
            if (result != null) {
                if (result) {
                    Toast.makeText(getContext(), getString(R.string.save_success), Toast.LENGTH_SHORT).show();
                    // 返回上一页
                    Navigation.findNavController(requireView()).navigateUp();
                } else {
                    Toast.makeText(getContext(), getString(R.string.save_failed), Toast.LENGTH_SHORT).show();
                }
                // 重置结果
                addTransactionViewModel.resetSaveResult();
            }
        });

        // 观察错误信息
        addTransactionViewModel.getErrorMessage().observe(getViewLifecycleOwner(), errorMsg -> {
            if (errorMsg != null) {
                Toast.makeText(getContext(), errorMsg, Toast.LENGTH_LONG).show();
                // 重置错误信息
                addTransactionViewModel.resetErrorMessage();
            }
        });

        // 观察分类列表
        addTransactionViewModel.getCategories().observe(getViewLifecycleOwner(), categories -> {
            if (categories != null && categoryAdapter != null) {
                // 根据当前交易类型过滤分类
                TransactionType currentType = addTransactionViewModel.getTransactionType().getValue();
                if (currentType == null) {
                    currentType = TransactionType.EXPENSE;
                }

                List<Category> filteredCategories = new ArrayList<>();
                for (Category category : categories) {
                    if (category.getType() == currentType) {
                        filteredCategories.add(category);
                    }
                }
                categoryAdapter.updateCategories(filteredCategories);
            }
        });

        // 观察账户列表
        addTransactionViewModel.getAccounts().observe(getViewLifecycleOwner(), accounts -> {
            if (accounts != null && accountAdapter != null) {
                accountAdapter.updateAccounts(accounts);
            }
        });
    }

    /**
     * 设置初始状态
     */
    private void setupInitialState() {
        // 从参数中获取交易类型
        Bundle args = getArguments();
        TransactionType initialType = TransactionType.EXPENSE; // 默认为支出

        if (args != null && args.containsKey("transaction_type")) {
            String typeString = args.getString("transaction_type");
            if ("INCOME".equals(typeString)) {
                initialType = TransactionType.INCOME;
            } else if ("TRANSFER".equals(typeString)) {
                initialType = TransactionType.TRANSFER;
            }
        }

        // 设置初始交易类型
        addTransactionViewModel.setTransactionType(initialType);
    }

    /**
     * 保存交易
     */
    private void saveTransaction() {
        // 获取输入数据
        String amountText = etAmount.getText() != null ? etAmount.getText().toString().trim() : "";
        String categoryText = etCategory.getText() != null ? etCategory.getText().toString().trim() : "";
        String accountText = etAccount.getText() != null ? etAccount.getText().toString().trim() : "";
        String description = etDescription.getText() != null ? etDescription.getText().toString().trim() : "";
        String note = etNote.getText() != null ? etNote.getText().toString().trim() : "";

        // 验证输入
        if (amountText.isEmpty()) {
            tilAmount.setError(getString(R.string.please_enter_amount));
            return;
        }

        // 验证金额格式
        try {
            BigDecimal amount = new BigDecimal(amountText);
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                tilAmount.setError(getString(R.string.amount_must_be_positive));
                return;
            }
            if (amount.scale() > 2) {
                tilAmount.setError(getString(R.string.amount_max_two_decimals));
                return;
            }
        } catch (NumberFormatException e) {
            tilAmount.setError(getString(R.string.please_enter_valid_amount));
            return;
        }

        if (selectedCategory == null) {
            tilCategory.setError(getString(R.string.please_select_category));
            return;
        }

        if (selectedAccount == null) {
            tilAccount.setError(getString(R.string.please_select_account));
            return;
        }

        // 清除错误提示
        tilAmount.setError(null);
        tilCategory.setError(null);
        tilAccount.setError(null);

        // 调用ViewModel保存，传递ID而不是名称
        addTransactionViewModel.saveTransactionWithIds(amountText, selectedCategory.getId(), selectedAccount.getId(), description, note);
    }

    /**
     * 设置适配器
     */
    private void setupAdapters(View view) {
        // 初始化分类适配器
        categoryAdapter = new CategorySpinnerAdapter(requireContext(), new ArrayList<>());
        etCategory.setAdapter(categoryAdapter);

        // 设置分类选择监听器
        etCategory.setOnItemClickListener((parent, itemView, position, id) -> {
            selectedCategory = categoryAdapter.getItem(position);
            if (selectedCategory != null) {
                etCategory.setText(selectedCategory.getName(), false);
            }
        });

        // 初始化账户适配器
        accountAdapter = new AccountSpinnerAdapter(requireContext(), new ArrayList<>());
        etAccount.setAdapter(accountAdapter);

        // 设置账户选择监听器
        etAccount.setOnItemClickListener((parent, itemView, position, id) -> {
            selectedAccount = accountAdapter.getItem(position);
            if (selectedAccount != null) {
                etAccount.setText(selectedAccount.getName(), false);
            }
        });

        // 设置金额输入监听器
        moneyTextWatcher = new MoneyTextWatcher(etAmount);
        etAmount.addTextChangedListener(moneyTextWatcher);

        // 初始化数字键盘
        View keypadView = view.findViewById(R.id.layout_keypad);
        keypadHelper = new NumberKeypadHelper(etAmount, keypadView);

        // 设置金额输入框点击事件，显示数字键盘
        etAmount.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                keypadHelper.showKeypad();
                // 隐藏系统键盘
                etAmount.setShowSoftInputOnFocus(false);
            }
        });

        etAmount.setOnClickListener(v -> {
            keypadHelper.showKeypad();
            etAmount.setShowSoftInputOnFocus(false);
        });

        // 设置其他输入框的焦点监听，隐藏数字键盘
        setupOtherInputFocusListeners();
    }

    /**
     * 设置其他输入框的焦点监听器
     */
    private void setupOtherInputFocusListeners() {
        View.OnFocusChangeListener hideKeypadListener = (v, hasFocus) -> {
            if (hasFocus && keypadHelper != null) {
                keypadHelper.hideKeypad();
            }
        };

        etCategory.setOnFocusChangeListener(hideKeypadListener);
        etAccount.setOnFocusChangeListener(hideKeypadListener);
        etDescription.setOnFocusChangeListener(hideKeypadListener);
        etNote.setOnFocusChangeListener(hideKeypadListener);
    }

    /**
     * 更新分类列表
     */
    private void updateCategoryList(TransactionType type) {
        if (categoryAdapter != null) {
            List<Category> allCategories = addTransactionViewModel.getCategories().getValue();
            if (allCategories != null) {
                List<Category> filteredCategories = new ArrayList<>();
                for (Category category : allCategories) {
                    if (category.getType() == type) {
                        filteredCategories.add(category);
                    }
                }
                categoryAdapter.updateCategories(filteredCategories);

                // 清空当前选择的分类
                etCategory.setText("");
                selectedCategory = null;
            }
        }
    }
}
