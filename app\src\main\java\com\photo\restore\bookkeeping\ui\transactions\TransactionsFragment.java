package com.photo.restore.bookkeeping.ui.transactions;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.ui.adapter.TransactionWithDetailsAdapter;
import com.photo.restore.bookkeeping.ui.adapter.GroupedTransactionAdapter;
import com.photo.restore.bookkeeping.data.entity.TransactionWithDetails;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.datepicker.MaterialDatePicker;
import com.google.android.material.datepicker.CalendarConstraints;
import com.google.android.material.datepicker.DateValidatorPointBackward;
import androidx.core.util.Pair;
import android.widget.Toast;
import androidx.navigation.Navigation;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import android.text.TextWatcher;
import android.text.Editable;
import com.google.android.material.textfield.TextInputLayout;
import com.google.android.material.textfield.TextInputEditText;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.Locale;

import com.photo.restore.bookkeeping.data.enums.TransactionType;

/**
 * 交易列表Fragment
 * 显示所有交易记录，支持筛选和搜索
 */
public class TransactionsFragment extends Fragment {

    private TransactionsViewModel transactionsViewModel;
    
    // UI组件
    private MaterialToolbar toolbar;
    private MaterialButton btnFilterDate;
    private MaterialButton btnFilterType;
    private RecyclerView rvTransactions;
    private LinearLayout layoutEmpty;
    private TextInputLayout tilSearch;
    private TextInputEditText etSearch;
    
    // 适配器
    private GroupedTransactionAdapter transactionAdapter;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setHasOptionsMenu(true);

        // 初始化ViewModel
        transactionsViewModel = new ViewModelProvider(this).get(TransactionsViewModel.class);
    }

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_transactions, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupRecyclerView();
        setupClickListeners();
        observeData();
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        btnFilterDate = view.findViewById(R.id.btn_filter_date);
        btnFilterType = view.findViewById(R.id.btn_filter_type);
        rvTransactions = view.findViewById(R.id.rv_transactions);
        layoutEmpty = view.findViewById(R.id.layout_empty);
        tilSearch = view.findViewById(R.id.til_search);
        etSearch = view.findViewById(R.id.et_search);

        // 设置工具栏
        toolbar.setOnMenuItemClickListener(this::onOptionsItemSelected);
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        transactionAdapter = new GroupedTransactionAdapter(new ArrayList<>(), transactionWithDetails -> {
            // 点击交易项目，可以导航到交易详情页面或编辑页面
            showTransactionOptions(transactionWithDetails);
        });

        rvTransactions.setLayoutManager(new LinearLayoutManager(getContext()));
        rvTransactions.setAdapter(transactionAdapter);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 日期筛选按钮
        btnFilterDate.setOnClickListener(v -> {
            // TODO: 显示日期选择器
            showDatePicker();
        });

        // 类型筛选按钮
        btnFilterType.setOnClickListener(v -> {
            // TODO: 显示类型选择器
            showTypeFilter();
        });

        // 设置搜索监听器
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                String keyword = s.toString().trim();
                transactionsViewModel.setSearchKeyword(keyword);
            }
        });
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        // 观察交易列表
        transactionsViewModel.getTransactions().observe(getViewLifecycleOwner(), transactions -> {
            if (transactions != null && !transactions.isEmpty()) {
                transactionAdapter.updateTransactions(transactions);
                rvTransactions.setVisibility(View.VISIBLE);
                layoutEmpty.setVisibility(View.GONE);
            } else {
                rvTransactions.setVisibility(View.GONE);
                layoutEmpty.setVisibility(View.VISIBLE);
            }
        });

        // 观察筛选状态
        transactionsViewModel.getDateFilterText().observe(getViewLifecycleOwner(), dateText -> {
            btnFilterDate.setText(dateText);
        });

        transactionsViewModel.getTypeFilterText().observe(getViewLifecycleOwner(), typeText -> {
            btnFilterType.setText(typeText);
        });
    }

    /**
     * 显示交易操作选项
     */
    private void showTransactionOptions(TransactionWithDetails transactionWithDetails) {
        String[] options = {getString(R.string.edit), getString(R.string.delete)};

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.select_operation))
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0: // 编辑
                            editTransaction(transactionWithDetails);
                            break;
                        case 1: // 删除
                            deleteTransaction(transactionWithDetails);
                            break;
                    }
                })
                .show();
    }

    /**
     * 编辑交易
     */
    private void editTransaction(TransactionWithDetails transactionWithDetails) {
        // TODO: 导航到编辑页面，传递交易ID
        // Bundle args = new Bundle();
        // args.putString("transaction_id", transactionWithDetails.transaction.getId());
        // Navigation.findNavController(requireView()).navigate(R.id.action_to_edit_transaction, args);
        Toast.makeText(getContext(), getString(R.string.edit_function_pending), Toast.LENGTH_SHORT).show();
    }

    /**
     * 删除交易
     */
    private void deleteTransaction(TransactionWithDetails transactionWithDetails) {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.confirm_delete))
                .setMessage(getString(R.string.delete_transaction_message))
                .setPositiveButton(getString(R.string.delete), (dialog, which) -> {
                    transactionsViewModel.deleteTransaction(transactionWithDetails.transaction.getId());
                    Toast.makeText(getContext(), getString(R.string.transaction_deleted), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示日期选择器
     */
    private void showDatePicker() {
        // 创建日期范围选择器
        MaterialDatePicker<Pair<Long, Long>> dateRangePicker = MaterialDatePicker.Builder
                .dateRangePicker()
                .setTitleText(getString(R.string.select_date_range))
                .setSelection(Pair.create(
                        MaterialDatePicker.thisMonthInUtcMilliseconds(),
                        MaterialDatePicker.todayInUtcMilliseconds()
                ))
                .build();

        dateRangePicker.addOnPositiveButtonClickListener(selection -> {
            Date startDate = new Date(selection.first);
            Date endDate = new Date(selection.second);

            SimpleDateFormat formatter = new SimpleDateFormat("MM/dd", Locale.getDefault());
            String displayText = formatter.format(startDate) + " - " + formatter.format(endDate);

            transactionsViewModel.setDateFilter(startDate, endDate, displayText);
        });

        dateRangePicker.show(getParentFragmentManager(), "date_picker");
    }

    /**
     * 显示类型筛选器
     */
    private void showTypeFilter() {
        String[] types = {
            getString(R.string.all_types),
            getString(R.string.income),
            getString(R.string.expense)
        };
        int currentSelection = getCurrentTypeSelection();

        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.select_transaction_type))
                .setSingleChoiceItems(types, currentSelection, (dialog, which) -> {
                    switch (which) {
                        case 0: // 全部
                            transactionsViewModel.clearTypeFilter();
                            break;
                        case 1: // 收入
                            transactionsViewModel.setTypeFilter(TransactionType.INCOME, getString(R.string.income));
                            break;
                        case 2: // 支出
                            transactionsViewModel.setTypeFilter(TransactionType.EXPENSE, getString(R.string.expense));
                            break;
                    }
                    dialog.dismiss();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 获取当前类型选择
     */
    private int getCurrentTypeSelection() {
        String currentText = btnFilterType.getText().toString();
        if (currentText.equals(getString(R.string.income))) {
            return 1;
        } else if (currentText.equals(getString(R.string.expense))) {
            return 2;
        } else {
            return 0; // 全部
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 刷新数据
        transactionsViewModel.refreshData();
    }

    @Override
    public void onCreateOptionsMenu(@NonNull Menu menu, @NonNull MenuInflater inflater) {
        inflater.inflate(R.menu.menu_transactions, menu);
        super.onCreateOptionsMenu(menu, inflater);
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        int id = item.getItemId();

        if (id == R.id.action_search) {
            toggleSearchView();
            return true;
        } else if (id == R.id.action_export) {
            exportTransactions();
            return true;
        }

        return super.onOptionsItemSelected(item);
    }

    /**
     * 切换搜索视图显示状态
     */
    private void toggleSearchView() {
        if (tilSearch.getVisibility() == View.VISIBLE) {
            tilSearch.setVisibility(View.GONE);
            etSearch.setText("");
        } else {
            tilSearch.setVisibility(View.VISIBLE);
            etSearch.requestFocus();
        }
    }

    /**
     * 导出交易数据
     */
    private void exportTransactions() {
        // TODO: 实现导出功能
        Toast.makeText(getContext(), getString(R.string.export_function_pending), Toast.LENGTH_SHORT).show();
    }
}
