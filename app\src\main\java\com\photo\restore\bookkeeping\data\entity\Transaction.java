package com.photo.restore.bookkeeping.data.entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import com.photo.restore.bookkeeping.data.enums.TransactionType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 交易记录实体类
 */
@Entity(
    tableName = "transactions",
    foreignKeys = {
        @ForeignKey(
            entity = Account.class,
            parentColumns = "id",
            childColumns = "account_id",
            onDelete = ForeignKey.CASCADE
        ),
        @ForeignKey(
            entity = Category.class,
            parentColumns = "id",
            childColumns = "category_id",
            onDelete = ForeignKey.SET_NULL
        )
    },
    indices = {
        @Index("account_id"),
        @Index("category_id"),
        @Index("date"),
        @Index("type")
    }
)
public class Transaction {
    
    @PrimaryKey
    @NonNull
    @ColumnInfo(name = "id")
    private String id;
    
    @ColumnInfo(name = "account_id")
    private String accountId;
    
    @ColumnInfo(name = "category_id")
    private String categoryId;
    
    @ColumnInfo(name = "amount")
    private BigDecimal amount;
    
    @ColumnInfo(name = "currency")
    private String currency;
    
    @ColumnInfo(name = "type")
    private TransactionType type;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "note")
    private String note;
    
    @ColumnInfo(name = "image_data")
    private byte[] imageData;
    
    @ColumnInfo(name = "tags")
    private String tags; // JSON格式存储标签列表
    
    @ColumnInfo(name = "date")
    private Date date;
    
    @ColumnInfo(name = "location")
    private String location;
    
    @ColumnInfo(name = "created_at")
    private Date createdAt;
    
    @ColumnInfo(name = "updated_at")
    private Date updatedAt;

    // 构造函数
    public Transaction() {
        this.id = UUID.randomUUID().toString();
        this.amount = BigDecimal.ZERO;
        this.currency = "CNY";
        this.date = new Date();
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }

    public Transaction(String accountId, String categoryId, BigDecimal amount, 
                      TransactionType type, String description) {
        this();
        this.accountId = accountId;
        this.categoryId = categoryId;
        this.amount = amount;
        this.type = type;
        this.description = description;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public TransactionType getType() {
        return type;
    }

    public void setType(TransactionType type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public byte[] getImageData() {
        return imageData;
    }

    public void setImageData(byte[] imageData) {
        this.imageData = imageData;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * 获取交易类型的显示名称
     * @param isEnglish 是否为英文
     * @return 显示名称
     */
    public String getTypeDisplayName(boolean isEnglish) {
        return type != null ? type.getDisplayName(isEnglish) : "";
    }

    /**
     * 检查是否为收入
     * @return true如果是收入
     */
    public boolean isIncome() {
        return TransactionType.INCOME.equals(type);
    }

    /**
     * 检查是否为支出
     * @return true如果是支出
     */
    public boolean isExpense() {
        return TransactionType.EXPENSE.equals(type);
    }
}
