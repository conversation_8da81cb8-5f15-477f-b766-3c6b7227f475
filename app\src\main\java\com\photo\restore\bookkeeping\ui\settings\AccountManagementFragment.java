package com.photo.restore.bookkeeping.ui.settings;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.AutoCompleteTextView;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.navigation.Navigation;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.google.android.material.floatingactionbutton.FloatingActionButton;
import com.photo.restore.bookkeeping.R;
import com.photo.restore.bookkeeping.data.entity.Account;
import com.photo.restore.bookkeeping.data.enums.AccountType;
import com.photo.restore.bookkeeping.ui.adapter.AccountManagementAdapter;

import java.math.BigDecimal;
import java.util.ArrayList;

/**
 * 账户管理Fragment
 */
public class AccountManagementFragment extends Fragment {

    private AccountManagementViewModel viewModel;
    
    // UI组件
    private MaterialToolbar toolbar;
    private RecyclerView rvAccounts;
    private FloatingActionButton fabAddAccount;
    private View layoutEmpty;
    private MaterialButton btnAddFirstAccount;

    // 适配器
    private AccountManagementAdapter accountAdapter;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_account_management, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        initViewModel();
        setupRecyclerView();
        observeData();
    }

    /**
     * 初始化视图
     */
    private void initViews(View view) {
        toolbar = view.findViewById(R.id.toolbar);
        rvAccounts = view.findViewById(R.id.rv_accounts);
        fabAddAccount = view.findViewById(R.id.fab_add_account);
        layoutEmpty = view.findViewById(R.id.layout_empty);
        btnAddFirstAccount = view.findViewById(R.id.btn_add_first_account);

        // 设置返回按钮
        toolbar.setNavigationOnClickListener(v -> {
            Navigation.findNavController(view).navigateUp();
        });

        fabAddAccount.setOnClickListener(v -> showAddAccountDialog());
        btnAddFirstAccount.setOnClickListener(v -> showAddAccountDialog());
    }

    /**
     * 初始化ViewModel
     */
    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(AccountManagementViewModel.class);
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        accountAdapter = new AccountManagementAdapter(new ArrayList<>(), new AccountManagementAdapter.OnAccountActionListener() {
            @Override
            public void onEditAccount(Account account) {
                showEditAccountDialog(account);
            }

            @Override
            public void onDeleteAccount(Account account) {
                showDeleteAccountDialog(account);
            }

            @Override
            public void onToggleAccountStatus(Account account) {
                viewModel.toggleAccountStatus(account);
            }
        });

        rvAccounts.setLayoutManager(new LinearLayoutManager(getContext()));
        rvAccounts.setAdapter(accountAdapter);
    }

    /**
     * 观察数据变化
     */
    private void observeData() {
        viewModel.getAllAccounts().observe(getViewLifecycleOwner(), accounts -> {
            if (accounts != null) {
                accountAdapter.updateAccounts(accounts);
                updateEmptyState(accounts.isEmpty());
            }
        });
    }

    /**
     * 更新空状态显示
     */
    private void updateEmptyState(boolean isEmpty) {
        if (isEmpty) {
            rvAccounts.setVisibility(View.GONE);
            layoutEmpty.setVisibility(View.VISIBLE);
        } else {
            rvAccounts.setVisibility(View.VISIBLE);
            layoutEmpty.setVisibility(View.GONE);
        }
    }

    /**
     * 显示添加账户对话框
     */
    private void showAddAccountDialog() {
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_account, null);
        
        EditText etAccountName = dialogView.findViewById(R.id.et_account_name);
        AutoCompleteTextView spinnerAccountType = dialogView.findViewById(R.id.spinner_account_type);
        EditText etInitialBalance = dialogView.findViewById(R.id.et_initial_balance);

        // 设置账户类型Spinner
        String[] accountTypes = new String[AccountType.values().length];
        for (int i = 0; i < AccountType.values().length; i++) {
            accountTypes[i] = AccountType.values()[i].getDisplayName(requireContext());
        }
        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_dropdown_item_1line, accountTypes);
        spinnerAccountType.setAdapter(adapter);
        spinnerAccountType.setText(accountTypes[0], false);
        
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.add_account))
                .setView(dialogView)
                .setPositiveButton(getString(R.string.save), (dialog, which) -> {
                    String name = etAccountName.getText().toString().trim();
                    String balanceStr = etInitialBalance.getText().toString().trim();
                    
                    if (name.isEmpty()) {
                        Toast.makeText(getContext(), getString(R.string.account_name_required), Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    BigDecimal initialBalance = BigDecimal.ZERO;
                    if (!balanceStr.isEmpty()) {
                        try {
                            initialBalance = new BigDecimal(balanceStr);
                        } catch (NumberFormatException e) {
                            Toast.makeText(getContext(), getString(R.string.invalid_amount), Toast.LENGTH_SHORT).show();
                            return;
                        }
                    }
                    
                    // 获取选中的账户类型
                    String selectedTypeText = spinnerAccountType.getText().toString();
                    AccountType selectedType = AccountType.CASH; // 默认值
                    for (AccountType type : AccountType.values()) {
                        if (type.getDisplayName(requireContext()).equals(selectedTypeText)) {
                            selectedType = type;
                            break;
                        }
                    }
                    
                    Account newAccount = new Account();
                    newAccount.setName(name);
                    newAccount.setType(selectedType);
                    newAccount.setCurrency("CNY");
                    newAccount.setInitialBalance(initialBalance);
                    newAccount.setCurrentBalance(initialBalance);
                    newAccount.setActive(true);
                    
                    viewModel.addAccount(newAccount);
                    Toast.makeText(getContext(), getString(R.string.account_added_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示编辑账户对话框
     */
    private void showEditAccountDialog(Account account) {
        View dialogView = LayoutInflater.from(getContext()).inflate(R.layout.dialog_add_account, null);
        
        EditText etAccountName = dialogView.findViewById(R.id.et_account_name);
        AutoCompleteTextView spinnerAccountType = dialogView.findViewById(R.id.spinner_account_type);
        EditText etInitialBalance = dialogView.findViewById(R.id.et_initial_balance);

        // 设置账户类型Spinner
        String[] accountTypes = new String[AccountType.values().length];
        for (int i = 0; i < AccountType.values().length; i++) {
            accountTypes[i] = AccountType.values()[i].getDisplayName(requireContext());
        }
        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_dropdown_item_1line, accountTypes);
        spinnerAccountType.setAdapter(adapter);

        // 填充现有数据
        etAccountName.setText(account.getName());
        etInitialBalance.setText(account.getInitialBalance().toString());

        // 设置账户类型选择
        spinnerAccountType.setText(account.getType().getDisplayName(requireContext()), false);
        
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.edit_account))
                .setView(dialogView)
                .setPositiveButton(getString(R.string.save), (dialog, which) -> {
                    String name = etAccountName.getText().toString().trim();
                    String balanceStr = etInitialBalance.getText().toString().trim();
                    
                    if (name.isEmpty()) {
                        Toast.makeText(getContext(), getString(R.string.account_name_required), Toast.LENGTH_SHORT).show();
                        return;
                    }
                    
                    BigDecimal initialBalance = BigDecimal.ZERO;
                    if (!balanceStr.isEmpty()) {
                        try {
                            initialBalance = new BigDecimal(balanceStr);
                        } catch (NumberFormatException e) {
                            Toast.makeText(getContext(), getString(R.string.invalid_amount), Toast.LENGTH_SHORT).show();
                            return;
                        }
                    }
                    
                    // 获取选中的账户类型
                    String selectedTypeText = spinnerAccountType.getText().toString();
                    AccountType selectedType = account.getType(); // 默认保持原值
                    for (AccountType type : AccountType.values()) {
                        if (type.getDisplayName(requireContext()).equals(selectedTypeText)) {
                            selectedType = type;
                            break;
                        }
                    }
                    
                    account.setName(name);
                    account.setType(selectedType);
                    account.setInitialBalance(initialBalance);
                    
                    viewModel.updateAccount(account);
                    Toast.makeText(getContext(), getString(R.string.account_updated_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }

    /**
     * 显示删除账户确认对话框
     */
    private void showDeleteAccountDialog(Account account) {
        new MaterialAlertDialogBuilder(requireContext())
                .setTitle(getString(R.string.delete_account))
                .setMessage(getString(R.string.delete_account_confirmation, account.getName()))
                .setPositiveButton(getString(R.string.delete), (dialog, which) -> {
                    viewModel.deleteAccount(account);
                    Toast.makeText(getContext(), getString(R.string.account_deleted_successfully), Toast.LENGTH_SHORT).show();
                })
                .setNegativeButton(getString(R.string.cancel), null)
                .show();
    }
}
