<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Money App - 账单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#6C5CE7',
                        'primary-dark': '#5B4BCF',
                        'secondary': '#A29BFE',
                        'accent': '#FD79A8',
                        'success': '#00B894',
                        'warning': '#FDCB6E',
                        'danger': '#E84393',
                        'surface': '#FFFFFF',
                        'neutral-50': '#FAFBFC',
                        'neutral-100': '#F4F5F7',
                        'neutral-200': '#E3E5E8',
                        'neutral-300': '#CFD3D8',
                        'neutral-400': '#A6ACB5',
                        'neutral-500': '#7B8794',
                        'neutral-600': '#5A6575',
                        'neutral-700': '#3E4651',
                        'neutral-800': '#2D3843',
                        'neutral-900': '#1F2937'
                    },
                    fontFamily: {
                        'display': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'system-ui', 'sans-serif'],
                        'body': ['-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'system-ui', 'sans-serif']
                    },
                    borderRadius: {
                        '2xl': '1rem',
                        '3xl': '1.5rem',
                        '4xl': '2rem'
                    },
                    boxShadow: {
                        'soft': '0 2px 12px rgba(0, 0, 0, 0.04)',
                        'medium': '0 4px 24px rgba(0, 0, 0, 0.06)',
                        'large': '0 8px 40px rgba(0, 0, 0, 0.08)',
                        'card': '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 16px rgba(0, 0, 0, 0.04)',
                        'button': '0 2px 8px rgba(108, 92, 231, 0.2)',
                        'floating': '0 8px 32px rgba(108, 92, 231, 0.15)',
                        'glow': '0 0 20px rgba(108, 92, 231, 0.3)'
                    }
                }
            }
        }
    </script>
    <style>
        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: linear-gradient(145deg, #F8F9FA 0%, #E9ECEF 100%);
            border-radius: 40px;
            overflow: hidden;
            position: relative;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
        }
        
        .glass-card {
            backdrop-filter: blur(20px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 50%, #FD79A8 100%);
            position: relative;
            overflow: hidden;
        }
        
        .stats-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .filter-chip {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .filter-chip:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(108, 92, 231, 0.1);
        }
        
        .filter-chip.active {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            color: white;
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
            transform: translateY(-1px);
        }
        
        .transaction-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-left: 3px solid transparent;
        }
        
        .transaction-item:hover {
            transform: translateY(-2px) translateX(2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-left-color: #6C5CE7;
            background: linear-gradient(90deg, rgba(108, 92, 231, 0.02) 0%, rgba(255, 255, 255, 0.98) 100%);
        }
        
        .transaction-item:active {
            transform: scale(0.98);
            transition: all 0.1s;
        }
        
        .transaction-group {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .transaction-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.08);
        }
        
        .category-icon {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(108, 92, 231, 0.2);
        }
        
        .category-icon:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(108, 92, 231, 0.3);
        }
        
        .floating-action-mini {
            background: linear-gradient(135deg, #6C5CE7 0%, #A29BFE 100%);
            box-shadow: 0 4px 16px rgba(108, 92, 231, 0.3);
            transition: all 0.3s ease;
        }
        
        .floating-action-mini:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(108, 92, 231, 0.4);
        }
        
        .stat-item {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-width: 0;
            overflow: hidden;
        }
        
        .stat-item:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .stat-amount {
            font-size: 1rem;
            line-height: 1.2;
            word-break: break-all;
        }
        
        @media (max-width: 375px) {
            .stat-amount {
                font-size: 0.9rem;
            }
        }
        
        .swipe-actions {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-neutral-50 to-neutral-100 min-h-screen flex items-center justify-center font-body">
    <div class="phone-container">
        
        <!-- 状态栏 -->
        <div class="h-11 bg-transparent flex items-center justify-between px-6 text-neutral-800 text-sm font-medium">
            <span>9:41</span>
            <div class="flex items-center space-x-1">
                <i class="fas fa-signal text-xs"></i>
                <i class="fas fa-wifi text-xs"></i>
                <i class="fas fa-battery-three-quarters text-xs"></i>
            </div>
        </div>
        
        <!-- 导航栏 -->
        <div class="glass-card h-16 flex items-center justify-between px-4 mb-2 mx-4 rounded-2xl shadow-card">
            <h1 class="font-bold text-xl text-neutral-800 font-display">账单记录</h1>
            <div class="flex items-center space-x-2">
                <button class="floating-action-mini p-2 rounded-xl">
                    <i class="fas fa-search text-white text-sm"></i>
                </button>
                <button class="floating-action-mini p-2 rounded-xl">
                    <i class="fas fa-plus text-white text-sm"></i>
                </button>
            </div>
        </div>
        
        <!-- 月度统计 -->
        <div class="stats-card mx-4 mb-4 px-6 py-6 rounded-3xl shadow-floating relative">
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="font-bold text-white text-lg font-display">2024年6月</h3>
                    <button class="glass-card px-4 py-2 rounded-xl text-white text-sm font-semibold border border-white/20">
                        <i class="fas fa-calendar-alt mr-2"></i>选择月份
                </button>
                </div>
                <div class="grid grid-cols-3 gap-3">
                    <div class="stat-item text-center p-3 rounded-2xl">
                        <p class="stat-amount text-success font-bold font-display">¥8,500</p>
                        <p class="text-xs text-neutral-600 font-medium mt-1">收入</p>
                    </div>
                    <div class="stat-item text-center p-3 rounded-2xl">
                        <p class="stat-amount text-danger font-bold font-display">¥3,240</p>
                        <p class="text-xs text-neutral-600 font-medium mt-1">支出</p>
                    </div>
                    <div class="stat-item text-center p-3 rounded-2xl">
                        <p class="stat-amount text-primary font-bold font-display">¥5,260</p>
                        <p class="text-xs text-neutral-600 font-medium mt-1">结余</p>
                </div>
                </div>
            </div>
        </div>
        
        <!-- 筛选条件 -->
        <div class="px-4 mb-4">
            <div class="glass-card rounded-2xl p-4 shadow-card">
                <div class="flex space-x-3 overflow-x-auto">
                    <button class="filter-chip active px-5 py-3 rounded-xl text-sm font-semibold whitespace-nowrap">
                    全部
                </button>
                    <button class="filter-chip px-5 py-3 rounded-xl text-sm font-semibold text-neutral-600 whitespace-nowrap">
                    收入
                </button>
                    <button class="filter-chip px-5 py-3 rounded-xl text-sm font-semibold text-neutral-600 whitespace-nowrap">
                    支出
                </button>
                    <button class="filter-chip px-5 py-3 rounded-xl text-sm font-semibold text-neutral-600 whitespace-nowrap">
                        <i class="fas fa-filter mr-2"></i>筛选
                </button>
                    <button class="filter-chip px-5 py-3 rounded-xl text-sm font-semibold text-neutral-600 whitespace-nowrap">
                    餐饮
                </button>
                    <button class="filter-chip px-5 py-3 rounded-xl text-sm font-semibold text-neutral-600 whitespace-nowrap">
                    交通
                </button>
                </div>
            </div>
        </div>
        
        <!-- 交易列表 -->
        <div class="flex-1 overflow-y-auto px-4 space-y-6 pb-24">
            
            <!-- 今天 -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-bold text-neutral-700 font-display">今天</h3>
                    <span class="text-sm text-danger font-semibold">支出 ¥39.00</span>
                </div>
                <div class="transaction-group rounded-3xl p-4 shadow-card space-y-3">
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-warning to-orange-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-utensils text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">午餐 - 沙县小吃</p>
                                    <span class="text-danger font-bold text-lg">-¥35.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">餐饮 · 现金</p>
                                    <p class="text-xs text-neutral-500 font-medium">12:30</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-primary to-secondary rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-bus text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">地铁</p>
                                    <span class="text-danger font-bold text-lg">-¥4.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">交通 · 支付宝</p>
                                    <p class="text-xs text-neutral-500 font-medium">08:30</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 昨天 -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-bold text-neutral-700 font-display">昨天</h3>
                    <span class="text-sm text-success font-semibold">收入 ¥8,500.00</span>
                </div>
                <div class="transaction-group rounded-3xl p-4 shadow-card space-y-3">
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-success to-emerald-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-hand-holding-usd text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">工资</p>
                                    <span class="text-success font-bold text-lg">+¥8,500.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">薪资收入 · 银行卡</p>
                                    <p class="text-xs text-neutral-500 font-medium">09:00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-accent to-pink-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-film text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">电影票</p>
                                    <span class="text-danger font-bold text-lg">-¥80.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">娱乐 · 信用卡</p>
                                    <p class="text-xs text-neutral-500 font-medium">20:00</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-emerald-500 to-teal-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-shopping-bag text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">生活用品</p>
                                    <span class="text-danger font-bold text-lg">-¥120.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">购物 · 微信支付</p>
                                    <p class="text-xs text-neutral-500 font-medium">16:45</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 6月16日 -->
            <div>
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-bold text-neutral-700 font-display">6月16日</h3>
                    <span class="text-sm text-danger font-semibold">支出 ¥245.00</span>
                </div>
                <div class="transaction-group rounded-3xl p-4 shadow-card space-y-3">
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-red-500 to-rose-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-heartbeat text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">体检费用</p>
                                    <span class="text-danger font-bold text-lg">-¥200.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">医疗 · 银行卡</p>
                                    <p class="text-xs text-neutral-500 font-medium">14:20</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-warning to-amber-400 rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-coffee text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">下午茶</p>
                                    <span class="text-danger font-bold text-lg">-¥25.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">餐饮 · 支付宝</p>
                                    <p class="text-xs text-neutral-500 font-medium">15:30</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="transaction-item px-5 py-4 rounded-2xl">
                        <div class="flex items-center">
                            <div class="category-icon w-14 h-14 from-primary to-secondary rounded-2xl flex items-center justify-center mr-4">
                                <i class="fas fa-taxi text-white text-lg"></i>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between mb-2">
                                    <p class="font-bold text-neutral-800 font-display">打车</p>
                                    <span class="text-danger font-bold text-lg">-¥20.00</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <p class="text-xs text-neutral-500 font-medium">交通 · 微信支付</p>
                                    <p class="text-xs text-neutral-500 font-medium">18:45</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 加载更多 -->
            <div class="text-center pt-4">
                <button class="glass-card px-8 py-3 rounded-2xl text-primary font-semibold text-sm shadow-card hover:shadow-button transition-all duration-300 hover:scale-105">
                    加载更多记录
                </button>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="absolute bottom-0 left-0 right-0 glass-card border-t border-white/20 mx-4 mb-4 rounded-3xl shadow-floating">
            <div class="flex h-16">
                <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-300 hover:text-primary">
                <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs font-medium">首页</span>
            </button>
                <button class="flex-1 flex flex-col items-center justify-center text-primary relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl mx-2"></div>
                    <i class="fas fa-list text-lg mb-1 relative z-10"></i>
                    <span class="text-xs font-bold relative z-10">账单</span>
            </button>
                <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-300 hover:text-primary">
                <i class="fas fa-chart-pie text-lg mb-1"></i>
                    <span class="text-xs font-medium">统计</span>
            </button>
                <button class="flex-1 flex flex-col items-center justify-center text-neutral-400 transition-all duration-300 hover:text-primary">
                <i class="fas fa-cog text-lg mb-1"></i>
                    <span class="text-xs font-medium">设置</span>
            </button>
            </div>
        </div>
        
        <!-- 浮动记账按钮 -->
        <button class="absolute bottom-24 right-6 w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-2xl shadow-floating flex items-center justify-center transition-all duration-300 hover:scale-110 hover:shadow-glow">
            <i class="fas fa-plus text-white text-xl"></i>
        </button>
        
    </div>

    <script>
        // 筛选按钮交互
        document.querySelectorAll('.filter-chip').forEach(chip => {
            chip.addEventListener('click', function() {
                if (!this.classList.contains('active')) {
                    // 移除所有活动状态
                    document.querySelectorAll('.filter-chip').forEach(c => {
                        c.classList.remove('active');
                        c.classList.add('text-neutral-600');
                        c.classList.remove('text-white');
                    });
                    
                    // 添加活动状态到当前按钮
                    this.classList.add('active');
                    this.classList.remove('text-neutral-600');
                    this.classList.add('text-white');
                }
            });
        });
        
        // 交易项目点击事件（可以跳转到详情页）
        document.querySelectorAll('.transaction-item').forEach(item => {
            item.addEventListener('click', function() {
                // 这里可以添加跳转到交易详情页的逻辑
                console.log('查看交易详情');
            });
        });
        
        // 添加点击动画效果
        document.querySelectorAll('.floating-action-mini, .stat-item').forEach(element => {
            element.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        });
        
        // 浮动按钮动画
        const floatingButton = document.querySelector('.absolute.bottom-24');
        if (floatingButton) {
            floatingButton.addEventListener('click', function() {
                this.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 100);
            });
        }
        
        // 底部导航栏项目点击事件
        document.querySelectorAll('.flex.h-16 button').forEach(button => {
            button.addEventListener('click', function() {
                // 移除所有活动状态
                document.querySelectorAll('.flex.h-16 button').forEach(btn => {
                    btn.classList.remove('text-primary');
                    btn.classList.add('text-neutral-400');
                    const bg = btn.querySelector('.absolute');
                    if (bg) bg.remove();
                });
                
                // 添加活动状态到当前按钮
                this.classList.remove('text-neutral-400');
                this.classList.add('text-primary');
                
                // 添加背景指示器
                const indicator = document.createElement('div');
                indicator.className = 'absolute inset-0 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl mx-2';
                this.appendChild(indicator);
            });
        });
    </script>
</body>
</html> 