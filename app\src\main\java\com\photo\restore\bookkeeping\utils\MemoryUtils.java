package com.photo.restore.bookkeeping.utils;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Debug;
import android.util.Log;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内存管理工具类
 */
public class MemoryUtils {
    
    private static final String TAG = "MemoryUtils";
    private static final long MEMORY_CHECK_INTERVAL = 30; // 30秒检查一次内存
    private static final double MEMORY_WARNING_THRESHOLD = 0.8; // 内存使用超过80%时警告
    
    private static ScheduledExecutorService memoryMonitor;
    private static boolean isMonitoring = false;
    
    /**
     * 获取应用当前内存使用情况
     */
    public static MemoryInfo getMemoryInfo(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        ActivityManager.MemoryInfo memoryInfo = new ActivityManager.MemoryInfo();
        activityManager.getMemoryInfo(memoryInfo);
        
        // 获取应用内存使用情况
        Debug.MemoryInfo debugMemoryInfo = new Debug.MemoryInfo();
        Debug.getMemoryInfo(debugMemoryInfo);
        
        // 获取最大可用内存
        long maxMemory = Runtime.getRuntime().maxMemory();
        long totalMemory = Runtime.getRuntime().totalMemory();
        long freeMemory = Runtime.getRuntime().freeMemory();
        long usedMemory = totalMemory - freeMemory;
        
        return new MemoryInfo(
            maxMemory,
            totalMemory,
            usedMemory,
            freeMemory,
            memoryInfo.availMem,
            memoryInfo.totalMem,
            memoryInfo.lowMemory,
            debugMemoryInfo.getTotalPss() * 1024L // PSS转换为字节
        );
    }
    
    /**
     * 检查内存使用是否过高
     */
    public static boolean isMemoryUsageHigh(Context context) {
        MemoryInfo memoryInfo = getMemoryInfo(context);
        double usageRatio = (double) memoryInfo.usedMemory / memoryInfo.maxMemory;
        return usageRatio > MEMORY_WARNING_THRESHOLD;
    }
    
    /**
     * 执行垃圾回收
     */
    public static void performGC() {
        System.gc();
        System.runFinalization();
    }
    
    /**
     * 清理内存缓存
     */
    public static void clearMemoryCache() {
        // 清理图片缓存
        try {
            // 如果使用了Glide，可以清理Glide缓存
            // Glide.get(context).clearMemory();
        } catch (Exception e) {
            Log.w(TAG, "Failed to clear image cache", e);
        }
        
        // 执行垃圾回收
        performGC();
    }
    
    /**
     * 开始内存监控
     */
    public static void startMemoryMonitoring(Context context) {
        if (isMonitoring) {
            return;
        }
        
        isMonitoring = true;
        memoryMonitor = Executors.newSingleThreadScheduledExecutor();
        
        memoryMonitor.scheduleAtFixedRate(() -> {
            try {
                MemoryInfo memoryInfo = getMemoryInfo(context);
                double usageRatio = (double) memoryInfo.usedMemory / memoryInfo.maxMemory;
                
                Log.d(TAG, String.format("Memory usage: %.1f%% (%s / %s)", 
                    usageRatio * 100,
                    formatBytes(memoryInfo.usedMemory),
                    formatBytes(memoryInfo.maxMemory)));
                
                // 如果内存使用过高，执行清理
                if (usageRatio > MEMORY_WARNING_THRESHOLD) {
                    Log.w(TAG, "High memory usage detected, performing cleanup");
                    clearMemoryCache();
                }
                
                // 如果系统内存不足，也执行清理
                if (memoryInfo.isLowMemory) {
                    Log.w(TAG, "System low memory detected, performing cleanup");
                    clearMemoryCache();
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error during memory monitoring", e);
            }
        }, MEMORY_CHECK_INTERVAL, MEMORY_CHECK_INTERVAL, TimeUnit.SECONDS);
    }
    
    /**
     * 停止内存监控
     */
    public static void stopMemoryMonitoring() {
        if (memoryMonitor != null && !memoryMonitor.isShutdown()) {
            memoryMonitor.shutdown();
            isMonitoring = false;
        }
    }
    
    /**
     * 格式化字节数为可读格式
     */
    public static String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
    
    /**
     * 获取内存使用百分比
     */
    public static double getMemoryUsagePercentage(Context context) {
        MemoryInfo memoryInfo = getMemoryInfo(context);
        return (double) memoryInfo.usedMemory / memoryInfo.maxMemory * 100;
    }
    
    /**
     * 检查是否需要释放内存
     */
    public static boolean shouldReleaseMemory(Context context) {
        MemoryInfo memoryInfo = getMemoryInfo(context);
        return memoryInfo.isLowMemory || isMemoryUsageHigh(context);
    }
    
    /**
     * 内存信息数据类
     */
    public static class MemoryInfo {
        public final long maxMemory;        // 应用最大可用内存
        public final long totalMemory;      // 应用当前分配内存
        public final long usedMemory;       // 应用已使用内存
        public final long freeMemory;       // 应用空闲内存
        public final long systemAvailMemory; // 系统可用内存
        public final long systemTotalMemory; // 系统总内存
        public final boolean isLowMemory;   // 系统是否内存不足
        public final long pssMemory;        // PSS内存（实际物理内存使用）
        
        public MemoryInfo(long maxMemory, long totalMemory, long usedMemory, long freeMemory,
                         long systemAvailMemory, long systemTotalMemory, boolean isLowMemory, long pssMemory) {
            this.maxMemory = maxMemory;
            this.totalMemory = totalMemory;
            this.usedMemory = usedMemory;
            this.freeMemory = freeMemory;
            this.systemAvailMemory = systemAvailMemory;
            this.systemTotalMemory = systemTotalMemory;
            this.isLowMemory = isLowMemory;
            this.pssMemory = pssMemory;
        }
        
        @Override
        public String toString() {
            return String.format("MemoryInfo{used=%s, max=%s, usage=%.1f%%, lowMemory=%s}",
                formatBytes(usedMemory),
                formatBytes(maxMemory),
                (double) usedMemory / maxMemory * 100,
                isLowMemory);
        }
    }
    
    /**
     * 内存监控回调接口
     */
    public interface MemoryMonitorCallback {
        void onMemoryWarning(MemoryInfo memoryInfo);
        void onLowMemory(MemoryInfo memoryInfo);
    }
    
    /**
     * 设置内存监控回调
     */
    private static MemoryMonitorCallback memoryCallback;
    
    public static void setMemoryMonitorCallback(MemoryMonitorCallback callback) {
        memoryCallback = callback;
    }
    
    /**
     * 触发内存警告回调
     */
    private static void triggerMemoryWarning(MemoryInfo memoryInfo) {
        if (memoryCallback != null) {
            memoryCallback.onMemoryWarning(memoryInfo);
        }
    }
    
    /**
     * 触发低内存回调
     */
    private static void triggerLowMemory(MemoryInfo memoryInfo) {
        if (memoryCallback != null) {
            memoryCallback.onLowMemory(memoryInfo);
        }
    }
}
